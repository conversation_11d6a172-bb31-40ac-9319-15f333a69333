# 用户登录模块配置示例 (简化版)
# 版本: v2.0-simple
# 说明: 基于现有项目的简化配置

# 认证配置
auth:
  # JWT配置 (基本配置)
  jwt:
    secret: "xinye-x2-jwt-secret-key-2025"
    access-token-expiration: 3600  # 1小时
    refresh-token-expiration: 7200 # 2小时
    token-header: "Authorization"

  # 安全配置 (简化)
  security:
    max-login-attempts: 5          # 最大登录失败次数
    account-lock-minutes: 30       # 账号锁定时间（分钟）
    captcha-threshold: 3           # 需要验证码的失败次数阈值

  # 验证码配置 (基本配置)
  captcha:
    sms-length: 6                  # 短信验证码长度
    email-length: 6                # 邮箱验证码长度
    expiration: 300                # 过期时间（秒）
    daily-max-send: 10             # 每日最大发送次数
  
  # 第三方登录配置
  third-party:
    # 微信登录
    wechat:
      enabled: true
      app-id: "your_wechat_app_id"
      app-secret: "your_wechat_app_secret"
      redirect-uri: "https://your-domain.com/auth/wechat/callback"
      scope: "snsapi_userinfo"
      authorize-url: "https://open.weixin.qq.com/connect/oauth2/authorize"
      token-url: "https://api.weixin.qq.com/sns/oauth2/access_token"
      user-info-url: "https://api.weixin.qq.com/sns/userinfo"
    
    # QQ登录
    qq:
      enabled: true
      app-id: "your_qq_app_id"
      app-secret: "your_qq_app_secret"
      redirect-uri: "https://your-domain.com/auth/qq/callback"
      scope: "get_user_info"
      authorize-url: "https://graph.qq.com/oauth2.0/authorize"
      token-url: "https://graph.qq.com/oauth2.0/token"
      user-info-url: "https://graph.qq.com/user/get_user_info"
    
    # 新浪微博登录
    weibo:
      enabled: false
      app-id: "your_weibo_app_id"
      app-secret: "your_weibo_app_secret"
      redirect-uri: "https://your-domain.com/auth/weibo/callback"
      scope: "email"
      authorize-url: "https://api.weibo.com/oauth2/authorize"
      token-url: "https://api.weibo.com/oauth2/access_token"
      user-info-url: "https://api.weibo.com/2/users/show.json"
    
    # Apple ID登录
    apple:
      enabled: false
      app-id: "your_apple_app_id"
      team-id: "your_apple_team_id"
      key-id: "your_apple_key_id"
      private-key: "your_apple_private_key"
      redirect-uri: "https://your-domain.com/auth/apple/callback"
      scope: "name email"

# Redis配置（缓存相关）
spring:
  redis:
    host: localhost
    port: 6379
    password: ""
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

# 数据库配置
  datasource:
    url: ********************************************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

# 日志配置
logging:
  level:
    com.vida.xinye.x2.service.impl.AuthServiceImpl: DEBUG
    com.vida.xinye.x2.service.impl.SecurityManagerImpl: DEBUG
    com.vida.xinye.x2.service.impl.CacheManagerImpl: DEBUG
    com.vida.xinye.x2.controller.api.AuthController: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 邮件服务配置（用于邮箱验证码）
  mail:
    host: smtp.example.com
    port: 587
    username: <EMAIL>
    password: your_email_password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

# 短信服务配置（用于短信验证码）
sms:
  provider: aliyun  # 短信服务提供商：aliyun/tencent/huawei
  access-key-id: your_access_key_id
  access-key-secret: your_access_key_secret
  sign-name: "心叶教育"
  template-code: "SMS_123456789"

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 环境特定配置
---
# 开发环境
spring:
  profiles: dev
  
auth:
  jwt:
    secret: "xinye-x2-jwt-secret-key-2025-dev"
  security:
    enable-ip-whitelist: false
    max-login-attempts: 10  # 开发环境放宽限制
  captcha:
    enable-image-captcha: false  # 开发环境关闭验证码

logging:
  level:
    root: DEBUG

---
# 测试环境
spring:
  profiles: test
  
auth:
  jwt:
    secret: "xinye-x2-jwt-secret-key-2025-test"
  security:
    max-login-attempts: 3  # 测试环境严格限制
  captcha:
    enable-image-captcha: true

---
# 生产环境
spring:
  profiles: prod
  
auth:
  jwt:
    secret: "${JWT_SECRET:xinye-x2-jwt-secret-key-2025-production}"
  security:
    enable-ip-whitelist: true
    enable-secondary-auth: true
  captcha:
    enable-image-captcha: true
    enable-sms-captcha: true
    enable-email-captcha: true

logging:
  level:
    root: WARN
    com.vida.xinye.x2: INFO
