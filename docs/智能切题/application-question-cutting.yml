# 智能切题完整配置示例
# 复制此配置到你的 application.yml 文件中

question:
  cutting:
    # 天壤接口配置
    tianrang:
      url: https://api.tianrang.com/v1/question/detect
      api-key: ${TIANRANG_API_KEY:your_tianrang_api_key_here}
      timeout: 30000
      enabled: true
      retry-count: 2
    
    # 有道接口配置
    youdao:
      url: https://openapi.youdao.com/ocrapi
      app-key: ${YOUDAO_APP_KEY:your_youdao_app_key_here}
      app-secret: ${YOUDAO_APP_SECRET:your_youdao_app_secret_here}
      timeout: 30000
      enabled: true
      retry-count: 2
    
    # 阿里云接口配置
    aliyun:
      endpoint: ocr-api.cn-hangzhou.aliyuncs.com
      access-key-id: ${ALIYUN_ACCESS_KEY_ID:your_aliyun_access_key_id_here}
      access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:your_aliyun_access_key_secret_here}
      region-id: cn-hangzhou
      product: ocr-api
      version: 2021-07-07
      action: RecognizeEduPaperStructed
      need-rotate: false
      output-oricoord: false
      timeout: 30000
      enabled: true
      retry-count: 2
    
    # 通用配置
    common:
      # 默认切题策略
      # 可选值: BEST_QUALITY, FASTEST, AGGREGATED, SPECIFIED, ROUND_ROBIN
      default-strategy: BEST_QUALITY
      
      # 默认提供商（当strategy为SPECIFIED且未指定provider时使用）
      # 可选值: tianrang, youdao, aliyun
      default-provider: tianrang
      
      # 是否启用结果聚合
      enable-aggregation: true
      
      # 最大并发请求数
      max-concurrent-requests: 10
      
      # 图片最大尺寸（字节）10MB
      max-image-size: 10485760
      
      # 支持的图片格式
      supported-formats: [jpg, jpeg, png, bmp, gif]
      
      # 是否启用缓存
      enable-cache: true
      
      # 缓存过期时间（秒）
      cache-expire-seconds: 3600

---
# 开发环境配置示例
spring:
  profiles: dev

question:
  cutting:
    tianrang:
      url: https://api.tianrang.com/v1/question/detect
      api-key: ${TIANRANG_API_KEY:dev_tianrang_key}
      timeout: 30000
      enabled: true
      retry-count: 1
    
    youdao:
      url: https://openapi.youdao.com/ocrapi
      app-key: ${YOUDAO_APP_KEY:dev_youdao_key}
      app-secret: ${YOUDAO_APP_SECRET:dev_youdao_secret}
      timeout: 30000
      enabled: false  # 开发环境禁用有道
      retry-count: 1
    
    aliyun:
      endpoint: ocr-api.cn-hangzhou.aliyuncs.com
      access-key-id: ${ALIYUN_ACCESS_KEY_ID:dev_aliyun_id}
      access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:dev_aliyun_secret}
      region-id: cn-hangzhou
      product: ocr-api
      version: 2021-07-07
      action: RecognizeEduPaperStructed
      need-rotate: false
      output-oricoord: false
      timeout: 30000
      enabled: false  # 开发环境禁用阿里云
      retry-count: 1
    
    common:
      default-strategy: FASTEST  # 开发环境优先速度
      default-provider: tianrang
      enable-aggregation: false  # 开发环境禁用聚合
      max-concurrent-requests: 5
      max-image-size: 5242880  # 5MB
      supported-formats: [jpg, jpeg, png]
      enable-cache: false  # 开发环境禁用缓存
      cache-expire-seconds: 1800

---
# 测试环境配置示例
spring:
  profiles: test

question:
  cutting:
    tianrang:
      url: https://api.tianrang.com/v1/question/detect
      api-key: ${TIANRANG_API_KEY:test_tianrang_key}
      timeout: 30000
      enabled: true
      retry-count: 2
    
    youdao:
      url: https://openapi.youdao.com/ocrapi
      app-key: ${YOUDAO_APP_KEY:test_youdao_key}
      app-secret: ${YOUDAO_APP_SECRET:test_youdao_secret}
      timeout: 30000
      enabled: true
      retry-count: 2
    
    aliyun:
      endpoint: ocr-api.cn-hangzhou.aliyuncs.com
      access-key-id: ${ALIYUN_ACCESS_KEY_ID:test_aliyun_id}
      access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:test_aliyun_secret}
      timeout: 30000
      enabled: true
      retry-count: 2
    
    common:
      default-strategy: BEST_QUALITY
      default-provider: tianrang
      enable-aggregation: true
      max-concurrent-requests: 10
      max-image-size: 10485760  # 10MB
      supported-formats: [jpg, jpeg, png, bmp, gif]
      enable-cache: true
      cache-expire-seconds: 1800  # 30分钟

---
# 生产环境配置示例
spring:
  profiles: prod

question:
  cutting:
    tianrang:
      url: https://api.tianrang.com/v1/question/detect
      api-key: ${TIANRANG_API_KEY}  # 生产环境必须通过环境变量配置
      timeout: 30000
      enabled: true
      retry-count: 3
    
    youdao:
      url: https://openapi.youdao.com/ocrapi
      app-key: ${YOUDAO_APP_KEY}
      app-secret: ${YOUDAO_APP_SECRET}
      timeout: 30000
      enabled: true
      retry-count: 3
    
    aliyun:
      endpoint: ocr-api.cn-hangzhou.aliyuncs.com
      access-key-id: ${ALIYUN_ACCESS_KEY_ID}
      access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET}
      timeout: 30000
      enabled: true
      retry-count: 3
    
    common:
      default-strategy: AGGREGATED  # 生产环境使用聚合策略
      default-provider: tianrang
      enable-aggregation: true
      max-concurrent-requests: 20  # 生产环境提高并发数
      max-image-size: 10485760  # 10MB
      supported-formats: [jpg, jpeg, png, bmp, gif]
      enable-cache: true
      cache-expire-seconds: 3600  # 1小时

---
# 最小配置示例（仅启用天壤接口）
spring:
  profiles: minimal

question:
  cutting:
    tianrang:
      url: https://api.tianrang.com/v1/question/detect
      api-key: ${TIANRANG_API_KEY:your_tianrang_api_key}
      timeout: 30000
      enabled: true
      retry-count: 2
    
    youdao:
      enabled: false
    
    aliyun:
      enabled: false
    
    common:
      default-strategy: BEST_QUALITY
      default-provider: tianrang
      enable-aggregation: false
      max-concurrent-requests: 5
      max-image-size: 10485760
      supported-formats: [jpg, jpeg, png]
      enable-cache: true
      cache-expire-seconds: 3600
