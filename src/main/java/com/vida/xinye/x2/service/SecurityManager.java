package com.vida.xinye.x2.service;

import java.util.Map;

/**
 * 统一安全管理器接口
 * 整合登录安全、验证码、IP限制等功能
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
public interface SecurityManager {

    // ========== 登录安全管理 ==========

    /**
     * 检查账号是否被锁定
     *
     * @param account 账号
     * @return 是否被锁定
     */
    boolean isAccountLocked(String account);

    /**
     * 锁定账号
     *
     * @param account 账号
     * @param reason 锁定原因
     * @param duration 锁定时长（秒）
     * @return 是否成功
     */
    boolean lockAccount(String account, String reason, long duration);

    /**
     * 解锁账号
     *
     * @param account 账号
     * @return 是否成功
     */
    boolean unlockAccount(String account);

    /**
     * 记录登录失败
     *
     * @param account 账号
     * @param clientIp 客户端IP
     */
    void recordLoginFailure(String account, String clientIp);

    /**
     * 清除登录失败记录
     *
     * @param account 账号
     * @param clientIp 客户端IP
     */
    void clearLoginFailure(String account, String clientIp);

    /**
     * 获取登录失败次数
     *
     * @param account 账号
     * @param clientIp 客户端IP
     * @return 失败次数
     */
    int getLoginFailureCount(String account, String clientIp);

    // ========== IP安全管理 ==========

    /**
     * 检查IP是否被阻止
     *
     * @param clientIp 客户端IP
     * @return 是否被阻止
     */
    boolean isIpBlocked(String clientIp);

    /**
     * 阻止IP
     *
     * @param clientIp 客户端IP
     * @param reason 阻止原因
     * @param duration 阻止时长（秒）
     * @return 是否成功
     */
    boolean blockIp(String clientIp, String reason, long duration);

    /**
     * 解除IP阻止
     *
     * @param clientIp 客户端IP
     * @return 是否成功
     */
    boolean unblockIp(String clientIp);

    /**
     * 记录IP访问
     *
     * @param clientIp 客户端IP
     * @param action 访问动作
     */
    void recordIpAccess(String clientIp, String action);

    /**
     * 获取IP访问次数
     *
     * @param clientIp 客户端IP
     * @param timeWindow 时间窗口（秒）
     * @return 访问次数
     */
    int getIpAccessCount(String clientIp, long timeWindow);

    // ========== 验证码管理 ==========

    /**
     * 生成图形验证码
     *
     * @return 验证码信息（包含key和图片）
     */
    Map<String, Object> generateImageCaptcha();

    /**
     * 验证图形验证码
     *
     * @param key 验证码key
     * @param captcha 用户输入的验证码
     * @return 是否正确
     */
    boolean validateImageCaptcha(String key, String captcha);

    /**
     * 检查验证码发送限制
     *
     * @param account 账号
     * @return 是否允许发送
     */
    boolean checkCaptchaSendLimit(String account);

    /**
     * 记录验证码发送
     *
     * @param account 账号
     * @param type 验证码类型
     */
    void recordCaptchaSend(String account, String type);

    // ========== 设备管理 ==========

    /**
     * 检查设备是否可信
     *
     * @param deviceId 设备ID
     * @param userId 用户ID
     * @return 是否可信
     */
    boolean isDeviceTrusted(String deviceId, Long userId);

    /**
     * 添加可信设备
     *
     * @param deviceId 设备ID
     * @param userId 用户ID
     * @param deviceInfo 设备信息
     * @return 是否成功
     */
    boolean addTrustedDevice(String deviceId, Long userId, Map<String, Object> deviceInfo);

    /**
     * 移除可信设备
     *
     * @param deviceId 设备ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean removeTrustedDevice(String deviceId, Long userId);

    // ========== 审计日志 ==========

    /**
     * 记录登录历史
     *
     * @param userId 用户ID
     * @param account 账号
     * @param loginType 登录类型
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @param success 是否成功
     */
    void recordLoginHistory(Long userId, String account, String loginType, 
                          String clientIp, String userAgent, boolean success);

    /**
     * 记录安全事件
     *
     * @param userId 用户ID
     * @param eventType 事件类型
     * @param description 事件描述
     * @param clientIp 客户端IP
     * @param extraInfo 额外信息
     */
    void recordSecurityEvent(Long userId, String eventType, String description, 
                           String clientIp, Map<String, Object> extraInfo);

    /**
     * 获取用户登录历史
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 登录历史列表
     */
    java.util.List<Map<String, Object>> getUserLoginHistory(Long userId, int limit);

    // ========== 风险评估 ==========

    /**
     * 评估登录风险
     *
     * @param account 账号
     * @param clientIp 客户端IP
     * @param deviceId 设备ID
     * @param userAgent 用户代理
     * @return 风险评分（0-100）
     */
    int assessLoginRisk(String account, String clientIp, String deviceId, String userAgent);

    /**
     * 检查是否需要二次验证
     *
     * @param userId 用户ID
     * @param clientIp 客户端IP
     * @param deviceId 设备ID
     * @return 是否需要二次验证
     */
    boolean needSecondaryAuth(Long userId, String clientIp, String deviceId);

    // ========== 配置管理 ==========

    /**
     * 获取安全配置
     *
     * @return 安全配置
     */
    Map<String, Object> getSecurityConfig();

    /**
     * 更新安全配置
     *
     * @param config 配置信息
     * @return 是否成功
     */
    boolean updateSecurityConfig(Map<String, Object> config);

    // ========== 统计信息 ==========

    /**
     * 获取安全统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getSecurityStats();

    /**
     * 健康检查
     *
     * @return 健康状态
     */
    Map<String, Object> healthCheck();
}
