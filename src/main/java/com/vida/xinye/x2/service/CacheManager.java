package com.vida.xinye.x2.service;

import com.vida.xinye.x2.mbg.model.User;

import java.util.Map;

/**
 * 统一缓存管理器接口
 * 整合用户信息、Token、验证码等缓存功能
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
public interface CacheManager {

    // ========== 用户信息缓存 ==========

    /**
     * 缓存用户信息
     *
     * @param user 用户信息
     * @param expireSeconds 过期时间（秒）
     */
    void cacheUser(User user, int expireSeconds);

    /**
     * 缓存用户信息（使用默认过期时间）
     *
     * @param user 用户信息
     */
    default void cacheUser(User user) {
        cacheUser(user, 3600); // 默认1小时
    }

    /**
     * 获取缓存的用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    User getCachedUser(Long userId);

    /**
     * 根据账号获取缓存的用户信息
     *
     * @param account 账号
     * @return 用户信息
     */
    User getCachedUserByAccount(String account);

    /**
     * 删除用户缓存
     *
     * @param userId 用户ID
     */
    void removeCachedUser(Long userId);

    /**
     * 刷新用户缓存
     *
     * @param user 用户信息
     */
    void refreshUserCache(User user);

    // ========== Token缓存 ==========

    /**
     * 缓存访问令牌
     *
     * @param token 访问令牌
     * @param userId 用户ID
     * @param expireSeconds 过期时间（秒）
     */
    void cacheAccessToken(String token, Long userId, int expireSeconds);

    /**
     * 缓存刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @param userId 用户ID
     * @param expireSeconds 过期时间（秒）
     */
    void cacheRefreshToken(String refreshToken, Long userId, int expireSeconds);

    /**
     * 获取Token对应的用户ID
     *
     * @param token 访问令牌
     * @return 用户ID
     */
    Long getUserIdFromToken(String token);

    /**
     * 删除Token缓存
     *
     * @param token 令牌
     */
    void removeToken(String token);

    /**
     * 检查Token是否在黑名单中
     *
     * @param token 令牌
     * @return 是否在黑名单
     */
    boolean isTokenBlacklisted(String token);

    /**
     * 将Token加入黑名单
     *
     * @param token 令牌
     * @param expireSeconds 过期时间（秒）
     */
    void blacklistToken(String token, int expireSeconds);

    // ========== 验证码缓存 ==========

    /**
     * 缓存验证码
     *
     * @param account 账号
     * @param type 验证码类型
     * @param captcha 验证码
     * @param expireSeconds 过期时间（秒）
     */
    void cacheCaptcha(String account, String type, String captcha, int expireSeconds);

    /**
     * 获取验证码
     *
     * @param account 账号
     * @param type 验证码类型
     * @return 验证码
     */
    String getCaptcha(String account, String type);

    /**
     * 删除验证码
     *
     * @param account 账号
     * @param type 验证码类型
     */
    void removeCaptcha(String account, String type);

    /**
     * 缓存图形验证码
     *
     * @param key 验证码key
     * @param captcha 验证码值
     * @param expireSeconds 过期时间（秒）
     */
    void cacheImageCaptcha(String key, String captcha, int expireSeconds);

    /**
     * 获取图形验证码
     *
     * @param key 验证码key
     * @return 验证码值
     */
    String getImageCaptcha(String key);

    /**
     * 删除图形验证码
     *
     * @param key 验证码key
     */
    void removeImageCaptcha(String key);

    // ========== 登录安全缓存 ==========

    /**
     * 缓存登录失败次数
     *
     * @param account 账号
     * @param clientIp 客户端IP
     * @param count 失败次数
     * @param expireSeconds 过期时间（秒）
     */
    void cacheLoginFailureCount(String account, String clientIp, int count, int expireSeconds);

    /**
     * 获取登录失败次数
     *
     * @param account 账号
     * @param clientIp 客户端IP
     * @return 失败次数
     */
    int getLoginFailureCount(String account, String clientIp);

    /**
     * 清除登录失败记录
     *
     * @param account 账号
     * @param clientIp 客户端IP
     */
    void clearLoginFailureCount(String account, String clientIp);

    /**
     * 缓存账号锁定信息
     *
     * @param account 账号
     * @param lockInfo 锁定信息
     * @param expireSeconds 过期时间（秒）
     */
    void cacheAccountLock(String account, Map<String, Object> lockInfo, int expireSeconds);

    /**
     * 获取账号锁定信息
     *
     * @param account 账号
     * @return 锁定信息
     */
    Map<String, Object> getAccountLockInfo(String account);

    /**
     * 删除账号锁定信息
     *
     * @param account 账号
     */
    void removeAccountLock(String account);

    // ========== 会话管理 ==========

    /**
     * 缓存用户会话
     *
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @param sessionInfo 会话信息
     * @param expireSeconds 过期时间（秒）
     */
    void cacheUserSession(Long userId, String sessionId, Map<String, Object> sessionInfo, int expireSeconds);

    /**
     * 获取用户会话
     *
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @return 会话信息
     */
    Map<String, Object> getUserSession(Long userId, String sessionId);

    /**
     * 获取用户所有会话
     *
     * @param userId 用户ID
     * @return 会话列表
     */
    java.util.List<Map<String, Object>> getUserAllSessions(Long userId);

    /**
     * 删除用户会话
     *
     * @param userId 用户ID
     * @param sessionId 会话ID
     */
    void removeUserSession(Long userId, String sessionId);

    /**
     * 删除用户所有会话
     *
     * @param userId 用户ID
     */
    void removeUserAllSessions(Long userId);

    // ========== 通用缓存操作 ==========

    /**
     * 设置缓存
     *
     * @param key 缓存key
     * @param value 缓存值
     * @param expireSeconds 过期时间（秒）
     */
    void set(String key, Object value, int expireSeconds);

    /**
     * 获取缓存
     *
     * @param key 缓存key
     * @return 缓存值
     */
    Object get(String key);

    /**
     * 删除缓存
     *
     * @param key 缓存key
     */
    void delete(String key);

    /**
     * 检查缓存是否存在
     *
     * @param key 缓存key
     * @return 是否存在
     */
    boolean exists(String key);

    /**
     * 设置过期时间
     *
     * @param key 缓存key
     * @param expireSeconds 过期时间（秒）
     */
    void expire(String key, int expireSeconds);

    /**
     * 获取剩余过期时间
     *
     * @param key 缓存key
     * @return 剩余时间（秒）
     */
    long getExpire(String key);

    // ========== 批量操作 ==========

    /**
     * 批量设置缓存
     *
     * @param keyValues 键值对
     * @param expireSeconds 过期时间（秒）
     */
    void multiSet(Map<String, Object> keyValues, int expireSeconds);

    /**
     * 批量获取缓存
     *
     * @param keys 缓存key列表
     * @return 缓存值列表
     */
    java.util.List<Object> multiGet(java.util.List<String> keys);

    /**
     * 批量删除缓存
     *
     * @param keys 缓存key列表
     */
    void multiDelete(java.util.List<String> keys);

    // ========== 统计和监控 ==========

    /**
     * 获取缓存统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getCacheStats();

    /**
     * 清理过期缓存
     *
     * @return 清理的数量
     */
    int cleanExpiredCache();

    /**
     * 健康检查
     *
     * @return 健康状态
     */
    Map<String, Object> healthCheck();
}
