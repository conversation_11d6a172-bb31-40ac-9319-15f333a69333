package com.vida.xinye.x2.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.vida.xinye.x2.mbg.model.User;
import com.vida.xinye.x2.service.CacheManager;
import com.vida.xinye.x2.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 统一缓存管理器实现
 * 基于Redis实现的缓存管理
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
@Slf4j
@Service
public class CacheManagerImpl implements CacheManager {

    @Autowired
    private RedisService redisService;

    // 缓存键前缀
    private static final String USER_PREFIX = "cache:user:";
    private static final String USER_ACCOUNT_PREFIX = "cache:user_account:";
    private static final String ACCESS_TOKEN_PREFIX = "cache:access_token:";
    private static final String REFRESH_TOKEN_PREFIX = "cache:refresh_token:";
    private static final String TOKEN_BLACKLIST_PREFIX = "cache:token_blacklist:";
    private static final String CAPTCHA_PREFIX = "cache:captcha:";
    private static final String IMAGE_CAPTCHA_PREFIX = "cache:image_captcha:";
    private static final String LOGIN_FAILURE_PREFIX = "cache:login_failure:";
    private static final String ACCOUNT_LOCK_PREFIX = "cache:account_lock:";
    private static final String USER_SESSION_PREFIX = "cache:user_session:";

    @Override
    public void cacheUser(User user, int expireSeconds) {
        if (user == null || user.getId() == null) {
            return;
        }

        try {
            String key = USER_PREFIX + user.getId();
            String userJson = JSONUtil.toJsonStr(user);
            redisService.set(key, userJson, expireSeconds);

            // 同时缓存账号到用户ID的映射
            if (StrUtil.isNotBlank(user.getEmail())) {
                String accountKey = USER_ACCOUNT_PREFIX + user.getEmail();
                redisService.set(accountKey, user.getId().toString(), expireSeconds);
            }
            if (StrUtil.isNotBlank(user.getPhone())) {
                String accountKey = USER_ACCOUNT_PREFIX + user.getPhone();
                redisService.set(accountKey, user.getId().toString(), expireSeconds);
            }
            if (StrUtil.isNotBlank(user.getUsername())) {
                String accountKey = USER_ACCOUNT_PREFIX + user.getUsername();
                redisService.set(accountKey, user.getId().toString(), expireSeconds);
            }

            log.debug("缓存用户信息成功 - 用户ID: {}", user.getId());
        } catch (Exception e) {
            log.error("缓存用户信息失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public User getCachedUser(Long userId) {
        if (userId == null) {
            return null;
        }

        try {
            String key = USER_PREFIX + userId;
            Object userJson = redisService.get(key);
            if (userJson != null) {
                return JSONUtil.toBean(userJson.toString(), User.class);
            }
        } catch (Exception e) {
            log.error("获取缓存用户信息失败: {}", e.getMessage(), e);
        }

        return null;
    }

    @Override
    public User getCachedUserByAccount(String account) {
        if (StrUtil.isBlank(account)) {
            return null;
        }

        try {
            String accountKey = USER_ACCOUNT_PREFIX + account;
            Object userIdObj = redisService.get(accountKey);
            if (userIdObj != null) {
                Long userId = Long.parseLong(userIdObj.toString());
                return getCachedUser(userId);
            }
        } catch (Exception e) {
            log.error("根据账号获取缓存用户信息失败: {}", e.getMessage(), e);
        }

        return null;
    }

    @Override
    public void removeCachedUser(Long userId) {
        if (userId == null) {
            return;
        }

        try {
            // 先获取用户信息，以便删除账号映射
            User user = getCachedUser(userId);
            
            String key = USER_PREFIX + userId;
            redisService.del(key);

            // 删除账号映射
            if (user != null) {
                if (StrUtil.isNotBlank(user.getEmail())) {
                    redisService.del(USER_ACCOUNT_PREFIX + user.getEmail());
                }
                if (StrUtil.isNotBlank(user.getPhone())) {
                    redisService.del(USER_ACCOUNT_PREFIX + user.getPhone());
                }
                if (StrUtil.isNotBlank(user.getUsername())) {
                    redisService.del(USER_ACCOUNT_PREFIX + user.getUsername());
                }
            }

            log.debug("删除用户缓存成功 - 用户ID: {}", userId);
        } catch (Exception e) {
            log.error("删除用户缓存失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void refreshUserCache(User user) {
        if (user == null || user.getId() == null) {
            return;
        }

        // 先删除旧缓存，再设置新缓存
        removeCachedUser(user.getId());
        cacheUser(user);
    }

    @Override
    public void cacheAccessToken(String token, Long userId, int expireSeconds) {
        if (StrUtil.isBlank(token) || userId == null) {
            return;
        }

        try {
            String key = ACCESS_TOKEN_PREFIX + token;
            redisService.set(key, userId.toString(), expireSeconds);
            log.debug("缓存访问令牌成功 - 用户ID: {}", userId);
        } catch (Exception e) {
            log.error("缓存访问令牌失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void cacheRefreshToken(String refreshToken, Long userId, int expireSeconds) {
        if (StrUtil.isBlank(refreshToken) || userId == null) {
            return;
        }

        try {
            String key = REFRESH_TOKEN_PREFIX + refreshToken;
            redisService.set(key, userId.toString(), expireSeconds);
            log.debug("缓存刷新令牌成功 - 用户ID: {}", userId);
        } catch (Exception e) {
            log.error("缓存刷新令牌失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public Long getUserIdFromToken(String token) {
        if (StrUtil.isBlank(token)) {
            return null;
        }

        try {
            String key = ACCESS_TOKEN_PREFIX + token;
            Object userIdObj = redisService.get(key);
            if (userIdObj != null) {
                return Long.parseLong(userIdObj.toString());
            }
        } catch (Exception e) {
            log.error("从Token获取用户ID失败: {}", e.getMessage(), e);
        }

        return null;
    }

    @Override
    public void removeToken(String token) {
        if (StrUtil.isBlank(token)) {
            return;
        }

        try {
            redisService.del(ACCESS_TOKEN_PREFIX + token);
            redisService.del(REFRESH_TOKEN_PREFIX + token);
            log.debug("删除Token缓存成功");
        } catch (Exception e) {
            log.error("删除Token缓存失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public boolean isTokenBlacklisted(String token) {
        if (StrUtil.isBlank(token)) {
            return false;
        }

        try {
            String key = TOKEN_BLACKLIST_PREFIX + token;
            Object value = redisService.get(key);
            return value != null;
        } catch (Exception e) {
            log.error("检查Token黑名单失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void blacklistToken(String token, int expireSeconds) {
        if (StrUtil.isBlank(token)) {
            return;
        }

        try {
            String key = TOKEN_BLACKLIST_PREFIX + token;
            redisService.set(key, "blacklisted", expireSeconds);
            log.debug("Token加入黑名单成功");
        } catch (Exception e) {
            log.error("Token加入黑名单失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void cacheCaptcha(String account, String type, String captcha, int expireSeconds) {
        if (StrUtil.isBlank(account) || StrUtil.isBlank(type) || StrUtil.isBlank(captcha)) {
            return;
        }

        try {
            String key = CAPTCHA_PREFIX + type + ":" + account;
            redisService.set(key, captcha, expireSeconds);
            log.debug("缓存验证码成功 - 账号: {}, 类型: {}", account, type);
        } catch (Exception e) {
            log.error("缓存验证码失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public String getCaptcha(String account, String type) {
        if (StrUtil.isBlank(account) || StrUtil.isBlank(type)) {
            return null;
        }

        try {
            String key = CAPTCHA_PREFIX + type + ":" + account;
            Object captcha = redisService.get(key);
            return captcha != null ? captcha.toString() : null;
        } catch (Exception e) {
            log.error("获取验证码失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public void removeCaptcha(String account, String type) {
        if (StrUtil.isBlank(account) || StrUtil.isBlank(type)) {
            return;
        }

        try {
            String key = CAPTCHA_PREFIX + type + ":" + account;
            redisService.del(key);
            log.debug("删除验证码成功 - 账号: {}, 类型: {}", account, type);
        } catch (Exception e) {
            log.error("删除验证码失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void cacheImageCaptcha(String key, String captcha, int expireSeconds) {
        if (StrUtil.isBlank(key) || StrUtil.isBlank(captcha)) {
            return;
        }

        try {
            String cacheKey = IMAGE_CAPTCHA_PREFIX + key;
            redisService.set(cacheKey, captcha, expireSeconds);
            log.debug("缓存图形验证码成功 - key: {}", key);
        } catch (Exception e) {
            log.error("缓存图形验证码失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public String getImageCaptcha(String key) {
        if (StrUtil.isBlank(key)) {
            return null;
        }

        try {
            String cacheKey = IMAGE_CAPTCHA_PREFIX + key;
            Object captcha = redisService.get(cacheKey);
            return captcha != null ? captcha.toString() : null;
        } catch (Exception e) {
            log.error("获取图形验证码失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public void removeImageCaptcha(String key) {
        if (StrUtil.isBlank(key)) {
            return;
        }

        try {
            String cacheKey = IMAGE_CAPTCHA_PREFIX + key;
            redisService.del(cacheKey);
            log.debug("删除图形验证码成功 - key: {}", key);
        } catch (Exception e) {
            log.error("删除图形验证码失败: {}", e.getMessage(), e);
        }
    }

    // 其他方法的简化实现...
    @Override
    public void cacheLoginFailureCount(String account, String clientIp, int count, int expireSeconds) {
        // 待实现
    }

    @Override
    public int getLoginFailureCount(String account, String clientIp) {
        return 0; // 待实现
    }

    @Override
    public void clearLoginFailureCount(String account, String clientIp) {
        // 待实现
    }

    @Override
    public void cacheAccountLock(String account, Map<String, Object> lockInfo, int expireSeconds) {
        // 待实现
    }

    @Override
    public Map<String, Object> getAccountLockInfo(String account) {
        return null; // 待实现
    }

    @Override
    public void removeAccountLock(String account) {
        // 待实现
    }

    @Override
    public void cacheUserSession(Long userId, String sessionId, Map<String, Object> sessionInfo, int expireSeconds) {
        // 待实现
    }

    @Override
    public Map<String, Object> getUserSession(Long userId, String sessionId) {
        return null; // 待实现
    }

    @Override
    public List<Map<String, Object>> getUserAllSessions(Long userId) {
        return new ArrayList<>(); // 待实现
    }

    @Override
    public void removeUserSession(Long userId, String sessionId) {
        // 待实现
    }

    @Override
    public void removeUserAllSessions(Long userId) {
        // 待实现
    }

    @Override
    public void set(String key, Object value, int expireSeconds) {
        try {
            redisService.set(key, value, expireSeconds);
        } catch (Exception e) {
            log.error("设置缓存失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public Object get(String key) {
        try {
            return redisService.get(key);
        } catch (Exception e) {
            log.error("获取缓存失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public void delete(String key) {
        try {
            redisService.del(key);
        } catch (Exception e) {
            log.error("删除缓存失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public boolean exists(String key) {
        try {
            return redisService.hasKey(key);
        } catch (Exception e) {
            log.error("检查缓存存在性失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void expire(String key, int expireSeconds) {
        try {
            redisService.expire(key, expireSeconds);
        } catch (Exception e) {
            log.error("设置缓存过期时间失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public long getExpire(String key) {
        try {
            return redisService.getExpire(key);
        } catch (Exception e) {
            log.error("获取缓存过期时间失败: {}", e.getMessage(), e);
            return -1;
        }
    }

    @Override
    public void multiSet(Map<String, Object> keyValues, int expireSeconds) {
        // 待实现
    }

    @Override
    public List<Object> multiGet(List<String> keys) {
        return new ArrayList<>(); // 待实现
    }

    @Override
    public void multiDelete(List<String> keys) {
        // 待实现
    }

    @Override
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("timestamp", new Date());
        stats.put("version", "2.0-unified");
        return stats;
    }

    @Override
    public int cleanExpiredCache() {
        return 0; // 待实现
    }

    @Override
    public Map<String, Object> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            // 测试Redis连接
            redisService.set("health_check", "ok", 10);
            Object value = redisService.get("health_check");
            boolean isHealthy = "ok".equals(value);
            
            health.put("status", isHealthy ? "UP" : "DOWN");
            health.put("redis", isHealthy);
            health.put("timestamp", new Date());
            
            if (isHealthy) {
                redisService.del("health_check");
            }
        } catch (Exception e) {
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", new Date());
        }
        
        return health;
    }
}
