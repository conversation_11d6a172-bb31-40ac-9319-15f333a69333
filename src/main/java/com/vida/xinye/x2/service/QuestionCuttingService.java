package com.vida.xinye.x2.service;

import com.vida.xinye.x2.dto.BatchQuestionCuttingResultDto;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import com.vida.xinye.x2.dto.param.QuestionCuttingRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 智能切题服务接口（简化版）
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
public interface QuestionCuttingService {

    /**
     * 统一的切题处理接口
     * 支持文件、URL、Base64三种输入方式
     *
     * @param request 切题请求
     * @return 切题结果
     */
    QuestionCuttingResultDto cutQuestion(QuestionCuttingRequest request);

    /**
     * 批量图片切题
     *
     * @param images 图片列表
     * @param request 切题参数模板
     * @param parallel 是否并行处理
     * @return 批量切题结果
     */
    BatchQuestionCuttingResultDto batchCutQuestion(
            List<MultipartFile> images,
            QuestionCuttingRequest request,
            Boolean parallel
    );

    /**
     * 获取支持的提供商列表
     *
     * @return 提供商列表
     */
    List<String> getSupportedProviders();

    /**
     * 获取支持的策略列表
     *
     * @return 策略列表
     */
    List<String> getSupportedStrategies();

    /**
     * 获取系统统计信息
     *
     * @return 统计信息
     */
    java.util.Map<String, Object> getSystemStats();

    // ========== 便捷方法（向后兼容） ==========

    /**
     * 文件上传切题（便捷方法）
     */
    default QuestionCuttingResultDto cutQuestionByFile(MultipartFile file, String strategy, String provider) {
        return cutQuestion(QuestionCuttingRequest.fromFile(file)
                .strategy(strategy)
                .provider(provider));
    }

    /**
     * URL切题（便捷方法）
     */
    default QuestionCuttingResultDto cutQuestionByUrl(String imageUrl, String strategy, String provider) {
        return cutQuestion(QuestionCuttingRequest.fromUrl(imageUrl)
                .strategy(strategy)
                .provider(provider));
    }

    /**
     * Base64切题（便捷方法）
     */
    default QuestionCuttingResultDto cutQuestionByBase64(String imageBase64, String strategy, String provider) {
        return cutQuestion(QuestionCuttingRequest.fromBase64(imageBase64)
                .strategy(strategy)
                .provider(provider));
    }
}
