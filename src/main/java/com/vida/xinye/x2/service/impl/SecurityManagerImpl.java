package com.vida.xinye.x2.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.vida.xinye.x2.constant.LoginWayConstant;
import com.vida.xinye.x2.service.RedisService;
import com.vida.xinye.x2.service.SecurityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import javax.imageio.ImageIO;

/**
 * 统一安全管理器实现
 * 整合原有的LoginSecurityManager和LoginSecurityServiceImpl功能
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
@Slf4j
@Service
public class SecurityManagerImpl implements SecurityManager {

    @Autowired
    private RedisService redisService;

    // 缓存键前缀
    private static final String ACCOUNT_LOCK_PREFIX = "security:account_lock:";
    private static final String LOGIN_FAILURE_PREFIX = "security:login_failure:";
    private static final String IP_BLOCK_PREFIX = "security:ip_block:";
    private static final String IP_ACCESS_PREFIX = "security:ip_access:";
    private static final String IMAGE_CAPTCHA_PREFIX = "security:image_captcha:";
    private static final String CAPTCHA_SEND_PREFIX = "security:captcha_send:";
    private static final String TRUSTED_DEVICE_PREFIX = "security:trusted_device:";

    @Override
    public boolean isAccountLocked(String account) {
        if (StrUtil.isBlank(account)) {
            return false;
        }

        try {
            String key = ACCOUNT_LOCK_PREFIX + account;
            Object lockInfo = redisService.get(key);
            return lockInfo != null;
        } catch (Exception e) {
            log.error("检查账号锁定状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean lockAccount(String account, String reason, long duration) {
        if (StrUtil.isBlank(account)) {
            return false;
        }

        try {
            String key = ACCOUNT_LOCK_PREFIX + account;
            Map<String, Object> lockInfo = new HashMap<>();
            lockInfo.put("reason", reason);
            lockInfo.put("lockTime", new Date());
            lockInfo.put("duration", duration);

            redisService.set(key, lockInfo, (int) duration);
            
            log.warn("账号已锁定 - 账号: {}, 原因: {}, 时长: {}秒", 
                    maskAccount(account), reason, duration);
            
            return true;
        } catch (Exception e) {
            log.error("锁定账号失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean unlockAccount(String account) {
        if (StrUtil.isBlank(account)) {
            return false;
        }

        try {
            String key = ACCOUNT_LOCK_PREFIX + account;
            redisService.del(key);
            
            log.info("账号已解锁 - 账号: {}", maskAccount(account));
            return true;
        } catch (Exception e) {
            log.error("解锁账号失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void recordLoginFailure(String account, String clientIp) {
        if (StrUtil.isBlank(account) || StrUtil.isBlank(clientIp)) {
            return;
        }

        try {
            // 记录账号失败次数
            String accountKey = LOGIN_FAILURE_PREFIX + "account:" + account;
            incrementCounter(accountKey, 24 * 3600); // 24小时过期

            // 记录IP失败次数
            String ipKey = LOGIN_FAILURE_PREFIX + "ip:" + clientIp;
            incrementCounter(ipKey, 24 * 3600);

            // 检查是否需要自动锁定
            int accountFailures = getLoginFailureCount(account, null);
            if (accountFailures >= LoginWayConstant.MAX_LOGIN_ATTEMPTS) {
                lockAccount(account, "登录失败次数过多", 
                          LoginWayConstant.ACCOUNT_LOCK_MINUTES * 60L);
            }

            log.warn("记录登录失败 - 账号: {}, IP: {}, 失败次数: {}", 
                    maskAccount(account), clientIp, accountFailures);

        } catch (Exception e) {
            log.error("记录登录失败异常: {}", e.getMessage(), e);
        }
    }

    @Override
    public void clearLoginFailure(String account, String clientIp) {
        if (StrUtil.isBlank(account)) {
            return;
        }

        try {
            String accountKey = LOGIN_FAILURE_PREFIX + "account:" + account;
            redisService.del(accountKey);

            if (StrUtil.isNotBlank(clientIp)) {
                String ipKey = LOGIN_FAILURE_PREFIX + "ip:" + clientIp;
                redisService.del(ipKey);
            }

            log.info("清除登录失败记录 - 账号: {}", maskAccount(account));
        } catch (Exception e) {
            log.error("清除登录失败记录异常: {}", e.getMessage(), e);
        }
    }

    @Override
    public int getLoginFailureCount(String account, String clientIp) {
        if (StrUtil.isBlank(account)) {
            return 0;
        }

        try {
            String key = LOGIN_FAILURE_PREFIX + "account:" + account;
            Object count = redisService.get(key);
            return count != null ? Integer.parseInt(count.toString()) : 0;
        } catch (Exception e) {
            log.error("获取登录失败次数异常: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public boolean isIpBlocked(String clientIp) {
        if (StrUtil.isBlank(clientIp)) {
            return false;
        }

        try {
            String key = IP_BLOCK_PREFIX + clientIp;
            Object blockInfo = redisService.get(key);
            return blockInfo != null;
        } catch (Exception e) {
            log.error("检查IP阻止状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean blockIp(String clientIp, String reason, long duration) {
        if (StrUtil.isBlank(clientIp)) {
            return false;
        }

        try {
            String key = IP_BLOCK_PREFIX + clientIp;
            Map<String, Object> blockInfo = new HashMap<>();
            blockInfo.put("reason", reason);
            blockInfo.put("blockTime", new Date());
            blockInfo.put("duration", duration);

            redisService.set(key, blockInfo, (int) duration);
            
            log.warn("IP已阻止 - IP: {}, 原因: {}, 时长: {}秒", clientIp, reason, duration);
            return true;
        } catch (Exception e) {
            log.error("阻止IP失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean unblockIp(String clientIp) {
        if (StrUtil.isBlank(clientIp)) {
            return false;
        }

        try {
            String key = IP_BLOCK_PREFIX + clientIp;
            redisService.del(key);
            
            log.info("IP已解除阻止 - IP: {}", clientIp);
            return true;
        } catch (Exception e) {
            log.error("解除IP阻止失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void recordIpAccess(String clientIp, String action) {
        if (StrUtil.isBlank(clientIp)) {
            return;
        }

        try {
            String key = IP_ACCESS_PREFIX + clientIp + ":" + action;
            incrementCounter(key, 3600); // 1小时过期
        } catch (Exception e) {
            log.error("记录IP访问异常: {}", e.getMessage(), e);
        }
    }

    @Override
    public int getIpAccessCount(String clientIp, long timeWindow) {
        if (StrUtil.isBlank(clientIp)) {
            return 0;
        }

        try {
            String key = IP_ACCESS_PREFIX + clientIp + ":*";
            // 这里简化实现，实际应该根据时间窗口计算
            Object count = redisService.get(key);
            return count != null ? Integer.parseInt(count.toString()) : 0;
        } catch (Exception e) {
            log.error("获取IP访问次数异常: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public Map<String, Object> generateImageCaptcha() {
        try {
            String captchaText = generateRandomText(4);
            String captchaKey = IdUtil.simpleUUID();
            
            // 生成验证码图片
            BufferedImage image = createCaptchaImage(captchaText);
            
            // 转换为Base64
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "png", baos);
            byte[] imageBytes = baos.toByteArray();
            String imageBase64 = Base64.getEncoder().encodeToString(imageBytes);
            
            // 缓存验证码
            String key = IMAGE_CAPTCHA_PREFIX + captchaKey;
            redisService.set(key, captchaText.toLowerCase(), 300); // 5分钟过期
            
            Map<String, Object> result = new HashMap<>();
            result.put("key", captchaKey);
            result.put("image", "data:image/png;base64," + imageBase64);
            result.put("expiresIn", 300);
            
            return result;
        } catch (Exception e) {
            log.error("生成图形验证码失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean validateImageCaptcha(String key, String captcha) {
        if (StrUtil.isBlank(key) || StrUtil.isBlank(captcha)) {
            return false;
        }

        try {
            String cacheKey = IMAGE_CAPTCHA_PREFIX + key;
            Object cachedCaptcha = redisService.get(cacheKey);
            
            if (cachedCaptcha != null && 
                cachedCaptcha.toString().toLowerCase().equals(captcha.toLowerCase())) {
                // 验证成功后删除验证码
                redisService.del(cacheKey);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("验证图形验证码失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean checkCaptchaSendLimit(String account) {
        if (StrUtil.isBlank(account)) {
            return false;
        }

        try {
            String key = CAPTCHA_SEND_PREFIX + account;
            Object count = redisService.get(key);
            int sendCount = count != null ? Integer.parseInt(count.toString()) : 0;
            
            // 每天最多发送50次
            return sendCount < LoginWayConstant.DAY_SMS_MAX_TOTAL_NUM;
        } catch (Exception e) {
            log.error("检查验证码发送限制失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void recordCaptchaSend(String account, String type) {
        if (StrUtil.isBlank(account)) {
            return;
        }

        try {
            String key = CAPTCHA_SEND_PREFIX + account;
            incrementCounter(key, 24 * 3600); // 24小时过期
        } catch (Exception e) {
            log.error("记录验证码发送异常: {}", e.getMessage(), e);
        }
    }

    // 辅助方法
    private void incrementCounter(String key, int expireSeconds) {
        Object count = redisService.get(key);
        int newCount = count != null ? Integer.parseInt(count.toString()) + 1 : 1;
        redisService.set(key, String.valueOf(newCount), expireSeconds);
    }

    private String maskAccount(String account) {
        if (StrUtil.isBlank(account) || account.length() <= 3) {
            return account;
        }
        
        if (account.contains("@")) {
            // 邮箱脱敏
            String[] parts = account.split("@");
            String username = parts[0];
            if (username.length() > 3) {
                username = username.substring(0, 2) + "***" + username.substring(username.length() - 1);
            }
            return username + "@" + parts[1];
        } else {
            // 手机号脱敏
            return account.substring(0, 3) + "****" + account.substring(account.length() - 2);
        }
    }

    private String generateRandomText(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder sb = new StringBuilder();
        ThreadLocalRandom random = ThreadLocalRandom.current();
        
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return sb.toString();
    }

    private BufferedImage createCaptchaImage(String text) {
        int width = 120;
        int height = 40;
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();
        
        // 设置背景
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, width, height);
        
        // 设置字体
        g.setFont(new Font("Arial", Font.BOLD, 20));
        g.setColor(Color.BLACK);
        
        // 绘制文字
        g.drawString(text, 20, 25);
        
        // 添加干扰线
        ThreadLocalRandom random = ThreadLocalRandom.current();
        for (int i = 0; i < 5; i++) {
            g.setColor(new Color(random.nextInt(256), random.nextInt(256), random.nextInt(256)));
            g.drawLine(random.nextInt(width), random.nextInt(height), 
                      random.nextInt(width), random.nextInt(height));
        }
        
        g.dispose();
        return image;
    }

    // 其他方法的简化实现
    @Override
    public boolean isDeviceTrusted(String deviceId, Long userId) {
        return false; // 待实现
    }

    @Override
    public boolean addTrustedDevice(String deviceId, Long userId, Map<String, Object> deviceInfo) {
        return false; // 待实现
    }

    @Override
    public boolean removeTrustedDevice(String deviceId, Long userId) {
        return false; // 待实现
    }

    @Override
    public void recordLoginHistory(Long userId, String account, String loginType, 
                                 String clientIp, String userAgent, boolean success) {
        // 待实现
    }

    @Override
    public void recordSecurityEvent(Long userId, String eventType, String description, 
                                  String clientIp, Map<String, Object> extraInfo) {
        // 待实现
    }

    @Override
    public List<Map<String, Object>> getUserLoginHistory(Long userId, int limit) {
        return new ArrayList<>(); // 待实现
    }

    @Override
    public int assessLoginRisk(String account, String clientIp, String deviceId, String userAgent) {
        return 0; // 待实现
    }

    @Override
    public boolean needSecondaryAuth(Long userId, String clientIp, String deviceId) {
        return false; // 待实现
    }

    @Override
    public Map<String, Object> getSecurityConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("maxLoginAttempts", LoginWayConstant.MAX_LOGIN_ATTEMPTS);
        config.put("accountLockMinutes", LoginWayConstant.ACCOUNT_LOCK_MINUTES);
        config.put("captchaExpireMinutes", LoginWayConstant.CAPTCHA_EXPIRE_MINUTES);
        config.put("daySmsMaxTotal", LoginWayConstant.DAY_SMS_MAX_TOTAL_NUM);
        return config;
    }

    @Override
    public boolean updateSecurityConfig(Map<String, Object> config) {
        return false; // 待实现
    }

    @Override
    public Map<String, Object> getSecurityStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("timestamp", new Date());
        stats.put("version", "2.0-unified");
        return stats;
    }

    @Override
    public Map<String, Object> healthCheck() {
        Map<String, Object> health = new HashMap<>();

        try {
            // 检查Redis连接 - 使用实际的Redis操作来测试
            boolean redisHealth = testRedisConnection();
            health.put("redis", redisHealth);

            health.put("status", redisHealth ? "UP" : "DOWN");
            health.put("timestamp", new Date());
        } catch (Exception e) {
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", new Date());
        }

        return health;
    }

    /**
     * 测试Redis连接
     */
    private boolean testRedisConnection() {
        try {
            String testKey = "health_check_" + System.currentTimeMillis();
            String testValue = "ok";

            // 测试写入
            redisService.set(testKey, testValue, 10);

            // 测试读取
            Object result = redisService.get(testKey);

            // 清理测试数据
            redisService.del(testKey);

            return testValue.equals(result);
        } catch (Exception e) {
            log.error("Redis健康检查失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
