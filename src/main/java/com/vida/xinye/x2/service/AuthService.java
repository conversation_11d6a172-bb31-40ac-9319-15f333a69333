package com.vida.xinye.x2.service;

import com.vida.xinye.x2.dto.param.LoginRequest;
import com.vida.xinye.x2.dto.result.AuthResult;
import com.vida.xinye.x2.mbg.model.User;

import java.util.List;
import java.util.Map;

/**
 * 统一认证服务接口
 * 整合所有登录、认证相关功能
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
public interface AuthService {

    // ========== 核心认证方法 ==========

    /**
     * 统一登录接口
     * 支持所有登录方式的统一入口
     *
     * @param request 登录请求
     * @return 认证结果
     */
    AuthResult login(LoginRequest request);

    /**
     * 刷新访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 认证结果
     */
    AuthResult refreshToken(String refreshToken);

    /**
     * 用户登出
     *
     * @param token 访问令牌
     * @return 是否成功
     */
    boolean logout(String token);

    /**
     * 验证访问令牌
     *
     * @param token 访问令牌
     * @return 是否有效
     */
    boolean validateToken(String token);

    /**
     * 从令牌获取用户信息
     *
     * @param token 访问令牌
     * @return 用户信息
     */
    User getUserFromToken(String token);

    // ========== 密码相关方法 ==========

    /**
     * 验证密码
     *
     * @param account 账号
     * @param password 密码
     * @return 用户信息（验证成功时）
     */
    User validatePassword(String account, String password);

    /**
     * 修改密码
     *
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean changePassword(Long userId, String oldPassword, String newPassword);

    /**
     * 重置密码
     *
     * @param account 账号
     * @param captcha 验证码
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean resetPassword(String account, String captcha, String newPassword);

    // ========== 验证码相关方法 ==========

    /**
     * 发送验证码
     *
     * @param account 账号（邮箱或手机号）
     * @param type 验证码类型（login, register, reset_password）
     * @return 是否发送成功
     */
    boolean sendCaptcha(String account, String type);

    /**
     * 验证验证码
     *
     * @param account 账号
     * @param captcha 验证码
     * @param type 验证码类型
     * @return 是否验证成功
     */
    boolean validateCaptcha(String account, String captcha, String type);

    // ========== 第三方登录方法 ==========

    /**
     * 微信登录
     *
     * @param accessToken 微信访问令牌
     * @param openId 微信OpenID
     * @param userInfo 用户信息（可选）
     * @return 认证结果
     */
    AuthResult weixinLogin(String accessToken, String openId, Map<String, Object> userInfo);

    /**
     * QQ登录
     *
     * @param accessToken QQ访问令牌
     * @param openId QQ OpenID
     * @param userInfo 用户信息（可选）
     * @return 认证结果
     */
    AuthResult qqLogin(String accessToken, String openId, Map<String, Object> userInfo);

    /**
     * Apple ID登录
     *
     * @param jwtToken Apple JWT令牌
     * @param userInfo 用户信息（可选）
     * @return 认证结果
     */
    AuthResult appleIdLogin(String jwtToken, Map<String, Object> userInfo);

    // ========== 用户管理方法 ==========

    /**
     * 用户注册
     *
     * @param request 注册请求（复用LoginRequest）
     * @return 认证结果
     */
    AuthResult register(LoginRequest request);

    /**
     * 检查账号是否存在
     *
     * @param account 账号
     * @return 是否存在
     */
    boolean accountExists(String account);

    /**
     * 根据账号获取用户
     *
     * @param account 账号
     * @return 用户信息
     */
    User getUserByAccount(String account);

    /**
     * 绑定第三方账号
     *
     * @param userId 用户ID
     * @param loginType 登录类型
     * @param openId 第三方OpenID
     * @param userInfo 第三方用户信息
     * @return 是否成功
     */
    boolean bindThirdPartyAccount(Long userId, String loginType, String openId, Map<String, Object> userInfo);

    /**
     * 解绑第三方账号
     *
     * @param userId 用户ID
     * @param loginType 登录类型
     * @return 是否成功
     */
    boolean unbindThirdPartyAccount(Long userId, String loginType);

    // ========== 安全相关方法 ==========

    /**
     * 检查登录安全性
     *
     * @param request 登录请求
     * @return 安全检查结果
     */
    Map<String, Object> checkLoginSecurity(LoginRequest request);

    /**
     * 记录登录历史
     *
     * @param userId 用户ID
     * @param request 登录请求
     * @param success 是否成功
     */
    void recordLoginHistory(Long userId, LoginRequest request, boolean success);

    /**
     * 获取登录失败次数
     *
     * @param account 账号
     * @param clientIp 客户端IP
     * @return 失败次数
     */
    int getLoginFailureCount(String account, String clientIp);

    /**
     * 锁定账号
     *
     * @param account 账号
     * @param reason 锁定原因
     * @param duration 锁定时长（秒）
     * @return 是否成功
     */
    boolean lockAccount(String account, String reason, long duration);

    /**
     * 解锁账号
     *
     * @param account 账号
     * @return 是否成功
     */
    boolean unlockAccount(String account);

    // ========== 会话管理方法 ==========

    /**
     * 获取用户活跃会话
     *
     * @param userId 用户ID
     * @return 会话列表
     */
    List<Map<String, Object>> getUserActiveSessions(Long userId);

    /**
     * 踢出用户会话
     *
     * @param userId 用户ID
     * @param sessionId 会话ID（为空则踢出所有会话）
     * @return 是否成功
     */
    boolean kickUserSession(Long userId, String sessionId);

    /**
     * 清理过期会话
     *
     * @return 清理的会话数量
     */
    int cleanExpiredSessions();

    // ========== 系统管理方法 ==========

    /**
     * 获取支持的登录方式
     *
     * @return 登录方式列表
     */
    List<String> getSupportedLoginTypes();

    /**
     * 获取认证统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getAuthStats();

    /**
     * 健康检查
     *
     * @return 健康状态
     */
    Map<String, Object> healthCheck();

    // ========== 便捷方法（向后兼容） ==========

    /**
     * 邮箱密码登录（便捷方法）
     */
    default AuthResult loginByEmailPassword(String email, String password, String clientIp) {
        return login(LoginRequest.emailPassword(email, password).clientIp(clientIp));
    }

    /**
     * 手机验证码登录（便捷方法）
     */
    default AuthResult loginByPhoneCaptcha(String phone, String captcha, String clientIp) {
        return login(LoginRequest.phoneCaptcha(phone, captcha).clientIp(clientIp));
    }

    /**
     * 第三方登录（便捷方法）
     */
    default AuthResult loginByThirdParty(String loginType, String accessToken, String openId, String clientIp) {
        return login(LoginRequest.thirdParty(loginType, accessToken, openId).clientIp(clientIp));
    }
}
