package com.vida.xinye.x2.service.impl;

import com.alibaba.nacos.common.http.param.MediaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vida.xinye.x2.dto.ImageEnhancementDto;
import com.vida.xinye.x2.dto.ImageEnhancementOptDto;
import com.vida.xinye.x2.service.ImageEnhancementService;
import com.vida.xinye.x2.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author：Chenjy
 * Date:2025/8/18
 * Description:
 */
@Slf4j
@Service
public class ImageEnhancementServiceImpl implements ImageEnhancementService {
    @Value("${ocr.app.id:}")
    private String appId;

    @Value("${ocr.app.secret:}")
    private String secretCode;

    @Value("${ocr.app.url:https://api.textin.com/ai/service/v1/crop_enhance_image}")
    private String baseUrl;

    @Autowired
    private HttpClientUtil httpClientUtil;

    private final ObjectMapper objectMapper;

    public ImageEnhancementServiceImpl(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 仅传图片时使用默认配置的便捷重载
     */
    public ImageEnhancementDto enhanceImage(byte[] imageData) {
        return recognize(imageData, ImageEnhancementOptDto.defaults());
    }

    /**
     * 使用 ImageEnhancementOptions 实体类的重载方法
     */
    public ImageEnhancementDto recognize(byte[] fileContent, ImageEnhancementOptDto options) {
        return recognize(fileContent, options.toHashMap());
    }

    public ImageEnhancementDto recognize(byte[] fileContent, Map<String, Object> options) {
        try {
            // 检查配置是否完整
            if (!isConfigurationValid()) {
                log.warn("OCR配置不完整，无法进行图像增强处理");
                return createErrorResponse("OCR_CONFIG_MISSING", "OCR配置不完整");
            }

            // Build URL with query parameters using Stream API
            String queryParams = options.entrySet().stream()
                    .map(entry -> entry.getKey() + "=" + entry.getValue().toString())
                    .reduce((param1, param2) -> param1 + "&" + param2)
                    .orElse("");
            String fullUrl = baseUrl + (queryParams.isEmpty() ? "" : "?" + queryParams);
            Map<String, String> headers = new HashMap<>();
            headers.put("x-ti-app-id", appId);
            headers.put("x-ti-secret-code", secretCode);
            headers.put("Content-Type", MediaType.APPLICATION_OCTET_STREAM);
            String response = httpClientUtil.postJson(fullUrl, fileContent, headers);
            return parseResponse(response);
        } catch (Exception e) {
            log.error("图像增强处理失败: {}", e.getMessage(), e);
            return createErrorResponse("PROCESSING_ERROR", "图像增强处理失败: " + e.getMessage());
        }
    }


    /**
     * 解析 OCR 响应
     */
    private ImageEnhancementDto parseResponse(String response) throws Exception {
        JsonNode jsonNode = objectMapper.readTree(response);

        // 直接映射到实际的响应结构
        ImageEnhancementDto apiResponse = new ImageEnhancementDto();

        // 设置基础字段
        if (jsonNode.has("x_request_id")) {
            apiResponse.setXRequestId(jsonNode.get("x_request_id").asText());
        }
        if (jsonNode.has("code")) {
            apiResponse.setCode(jsonNode.get("code").asInt());
        }
        if (jsonNode.has("message")) {
            apiResponse.setMessage(jsonNode.get("message").asText());
        }
        if (jsonNode.has("version")) {
            apiResponse.setVersion(jsonNode.get("version").asText());
        }
        if (jsonNode.has("duration")) {
            apiResponse.setDuration(jsonNode.get("duration").asLong());
        }

        // 解析 result 对象
        if (jsonNode.has("result")) {
            JsonNode resultNode = jsonNode.get("result");
            ImageEnhancementDto.Result result = new ImageEnhancementDto.Result();

            // 设置原图尺寸
            if (resultNode.has("origin_width")) {
                result.setOriginWidth(resultNode.get("origin_width").asInt());
            }
            if (resultNode.has("origin_height")) {
                result.setOriginHeight(resultNode.get("origin_height").asInt());
            }

            // 解析图片列表
            if (resultNode.has("image_list")) {
                JsonNode imageList = resultNode.get("image_list");
                List<ImageEnhancementDto.ProcessedImage> processedImages = new ArrayList<>();

                for (JsonNode imageNode : imageList) {
                    ImageEnhancementDto.ProcessedImage processedImage = new ImageEnhancementDto.ProcessedImage();

                    // 设置裁剪后的尺寸
                    if (imageNode.has("cropped_width")) {
                        processedImage.setCroppedWidth(imageNode.get("cropped_width").asInt());
                    }
                    if (imageNode.has("cropped_height")) {
                        processedImage.setCroppedHeight(imageNode.get("cropped_height").asInt());
                    }

                    // 设置Base64图片
                    if (imageNode.has("image")) {
                        processedImage.setImage(imageNode.get("image").asText());
                    }

                    // 设置角点坐标
                    if (imageNode.has("position")) {
                        JsonNode positionNode = imageNode.get("position");
                        List<Integer> position = new ArrayList<>();
                        for (JsonNode pos : positionNode) {
                            position.add(pos.asInt());
                        }
                        processedImage.setPosition(position);
                    }

                    // 设置角度
                    if (imageNode.has("angle")) {
                        processedImage.setAngle(imageNode.get("angle").asInt());
                    }

                    processedImages.add(processedImage);
                }

                result.setImageList(processedImages);
            }

            apiResponse.setResult(result);
        }

        return apiResponse;
    }

    /**
     * 检查OCR配置是否完整
     */
    private boolean isConfigurationValid() {
        return appId != null && !appId.trim().isEmpty() &&
               secretCode != null && !secretCode.trim().isEmpty() &&
               baseUrl != null && !baseUrl.trim().isEmpty();
    }

    /**
     * 创建错误响应
     */
    private ImageEnhancementDto createErrorResponse(String errorCode, String errorMessage) {
        ImageEnhancementDto errorDto = new ImageEnhancementDto();
        errorDto.setCode(-1); // 使用-1表示错误
        errorDto.setMessage(errorMessage);
        // 注意：ImageEnhancementDto没有setSuccess方法，通过code判断成功与否
        return errorDto;
    }

}
