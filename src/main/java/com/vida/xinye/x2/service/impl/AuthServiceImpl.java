package com.vida.xinye.x2.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.vida.xinye.x2.constant.LoginWayConstant;
import com.vida.xinye.x2.dto.param.LoginRequest;
import com.vida.xinye.x2.dto.result.AuthResult;
import com.vida.xinye.x2.mbg.model.User;
import com.vida.xinye.x2.service.AuthService;
import com.vida.xinye.x2.service.SecurityManager;
import com.vida.xinye.x2.service.impl.UserCacheServiceImpl;
import com.vida.xinye.x2.service.UserService;
import com.vida.xinye.x2.component.JwtTokenProvider;
import com.vida.xinye.x2.mbg.mapper.UserMapper;
import com.vida.xinye.x2.mbg.model.UserExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.List;

/**
 * 统一认证服务实现
 * 整合原有的AuthService和ExtendedAuthService功能
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
@Slf4j
@Service
public class AuthServiceImpl implements AuthService {

    @Autowired
    private UserService userService;

    @Autowired
    private SecurityManager securityService;

    @Autowired
    private UserCacheServiceImpl cacheService;

    @Autowired
    private JwtTokenProvider tokenProvider;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private UserMapper userMapper;

    @Override
    public AuthResult login(LoginRequest request) {
        String requestId = IdUtil.simpleUUID();
        log.info("开始处理登录请求 - ID: {}, 账号: {}, 类型: {}", 
                requestId, request.getAccount(), request.getLoginType());

        try {
            // 参数验证
            request.validate();

            // 安全检查
            Map<String, Object> securityCheck = checkLoginSecurity(request);
            if (!(Boolean) securityCheck.get("allowed")) {
                return AuthResult.failure((String) securityCheck.get("errorCode"), 
                                        (String) securityCheck.get("errorMessage"))
                                .requestId(requestId);
            }

            // 根据登录类型执行不同的认证逻辑
            AuthResult result = executeLogin(request);
            result.setRequestId(requestId);

            // 记录登录历史
            if (result.isLoginSuccess()) {
                recordLoginHistory(result.getUserId(), request, true);
                log.info("登录成功 - ID: {}, 用户: {}", requestId, result.getUserId());
            } else {
                recordLoginHistory(null, request, false);
                log.warn("登录失败 - ID: {}, 原因: {}", requestId, result.getErrorMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("登录处理异常 - ID: {}, 错误: {}", requestId, e.getMessage(), e);
            recordLoginHistory(null, request, false);
            return AuthResult.failure("INTERNAL_ERROR", "系统内部错误")
                            .requestId(requestId);
        }
    }

    /**
     * 执行具体的登录逻辑
     */
    private AuthResult executeLogin(LoginRequest request) {
        switch (request.getLoginType()) {
            case LoginWayConstant.LOGIN_BY_EMAIL_PASSWORD:
            case LoginWayConstant.LOGIN_BY_MOBILE_PASSWORD:
                return passwordLogin(request);

            case LoginWayConstant.LOGIN_BY_EMAIL:
            case LoginWayConstant.LOGIN_BY_SMS:
            case LoginWayConstant.LOGIN_BY_MOBILE:
                return captchaLogin(request);

            case LoginWayConstant.LOGIN_BY_WEIXIN:
                return weixinLogin(request.getAccessToken(), request.getOpenId(), 
                                 buildThirdPartyUserInfo(request));

            case LoginWayConstant.LOGIN_BY_QQ:
                return qqLogin(request.getAccessToken(), request.getOpenId(), 
                             buildThirdPartyUserInfo(request));

            case LoginWayConstant.LOGIN_BY_APPLE_ID:
                return appleIdLogin(request.getJwtToken(), buildThirdPartyUserInfo(request));

            default:
                return AuthResult.failure("UNSUPPORTED_LOGIN_TYPE", "不支持的登录类型");
        }
    }

    /**
     * 密码登录
     */
    private AuthResult passwordLogin(LoginRequest request) {
        User user = validatePassword(request.getAccount(), request.getPassword());
        if (user == null) {
            // 增加失败次数
            securityService.recordLoginFailure(request.getAccount(), request.getClientIp());
            return AuthResult.failure("PASSWORD_ERROR", "账号或密码错误");
        }

        return generateAuthResult(user, request);
    }

    /**
     * 验证码登录
     */
    private AuthResult captchaLogin(LoginRequest request) {
        // 验证验证码
        if (!validateCaptcha(request.getAccount(), request.getCaptcha(), "login")) {
            return AuthResult.failure("CAPTCHA_ERROR", "验证码错误或已过期");
        }

        // 获取或创建用户
        User user = getUserByAccount(request.getAccount());
        if (user == null) {
            // 自动注册
            user = autoRegisterUser(request);
            if (user == null) {
                return AuthResult.failure("REGISTER_FAILED", "自动注册失败");
            }
        }

        return generateAuthResult(user, request).newUser(user.getCreateTime().getTime() > System.currentTimeMillis() - 60000);
    }

    /**
     * 生成认证结果
     */
    private AuthResult generateAuthResult(User user, LoginRequest request) {
        // 生成Token
        String accessToken = tokenProvider.generateToken(user.getId().toString());
        String refreshToken = tokenProvider.generateRefreshToken(user.getId().toString());

        // 缓存用户信息
        cacheService.cacheUserInfo(user);
        cacheService.cacheLoginToken(user.getId(), accessToken, request.getDeviceId());

        // 清除登录失败记录
        securityService.clearLoginFailure(request.getAccount(), request.getClientIp());

        return AuthResult.loginSuccess(accessToken, refreshToken, user)
                .tokenExpires(3600L, 7200L) // 1小时访问令牌，2小时刷新令牌
                .loginInfo(request.getLoginType(), request.getClientIp(), request.getDeviceType());
    }

    @Override
    public AuthResult refreshToken(String refreshToken) {
        try {
            if (!tokenProvider.validateRefreshToken(refreshToken, null)) {
                return AuthResult.failure("INVALID_REFRESH_TOKEN", "刷新令牌无效");
            }

            // 从刷新令牌中解析用户ID (简化实现)
            String userIdStr = tokenProvider.extractUsername(refreshToken);
            Long userId = Long.parseLong(userIdStr);
            User user = userMapper.selectByPrimaryKey(userId);
            if (user == null) {
                return AuthResult.failure("USER_NOT_FOUND", "用户不存在");
            }

            String newAccessToken = tokenProvider.generateToken(user.getId().toString());
            String newRefreshToken = tokenProvider.generateRefreshToken(user.getId().toString());

            // 更新缓存
            cacheService.cacheLoginToken(userId, newAccessToken, null);

            return AuthResult.refreshSuccess(newAccessToken, newRefreshToken)
                    .tokenExpires(3600L, 7200L);

        } catch (Exception e) {
            log.error("刷新Token失败: {}", e.getMessage(), e);
            return AuthResult.failure("REFRESH_FAILED", "刷新令牌失败");
        }
    }

    @Override
    public boolean logout(String token) {
        try {
            if (StrUtil.isBlank(token)) {
                return false;
            }

            // 从缓存中移除Token
            // cacheService.removeToken(token); // 待实现

            // 将Token加入黑名单 (简化实现，暂时跳过)
            // tokenProvider.blacklistToken(token);

            return true;

        } catch (Exception e) {
            log.error("登出失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean validateToken(String token) {
        if (StrUtil.isBlank(token)) {
            return false;
        }

        try {
            String username = tokenProvider.extractUsername(token);
            return tokenProvider.validateToken(token, username);
        } catch (Exception e) {
            log.error("Token验证失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public User getUserFromToken(String token) {
        if (!validateToken(token)) {
            return null;
        }

        try {
            String userIdStr = tokenProvider.extractUsername(token);
            Long userId = Long.parseLong(userIdStr);

            // 先从缓存获取
            User user = cacheService.getCachedUserInfo(userId);
            if (user != null) {
                return user;
            }

            // 缓存未命中，从数据库获取
            user = userMapper.selectByPrimaryKey(userId);
            if (user != null) {
                cacheService.cacheUserInfo(user);
            }

            return user;

        } catch (Exception e) {
            log.error("从Token获取用户失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public User validatePassword(String account, String password) {
        if (StrUtil.isBlank(account) || StrUtil.isBlank(password)) {
            return null;
        }

        try {
            User user = getUserByAccount(account);
            if (user == null) {
                return null;
            }

            if (passwordEncoder.matches(password, user.getPassword())) {
                return user;
            }

            return null;

        } catch (Exception e) {
            log.error("密码验证失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean sendCaptcha(String account, String type) {
        try {
            // 检查发送频率限制
            if (!securityService.checkCaptchaSendLimit(account)) {
                return false;
            }

            // 生成验证码
            String captcha = generateCaptcha();

            // 缓存验证码 (使用Redis直接缓存)
            // cacheService.cacheCaptcha(account, type, captcha, 300); // 待实现

            // 发送验证码（邮箱或短信）
            if (account.contains("@")) {
                return sendEmailCaptcha(account, captcha, type);
            } else {
                return sendSmsCaptcha(account, captcha, type);
            }

        } catch (Exception e) {
            log.error("发送验证码失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean validateCaptcha(String account, String captcha, String type) {
        if (StrUtil.isBlank(account) || StrUtil.isBlank(captcha)) {
            return false;
        }

        try {
            // String cachedCaptcha = cacheService.getCaptcha(account, type);
            // if (cachedCaptcha != null && cachedCaptcha.equals(captcha)) {
            //     // 验证成功后删除验证码
            //     cacheService.removeCaptcha(account, type);
            //     return true;
            // }
            // 暂时返回true，待实现
            return true;

        } catch (Exception e) {
            log.error("验证码验证失败: {}", e.getMessage(), e);
            return false;
        }
    }

    // 辅助方法
    private Map<String, Object> buildThirdPartyUserInfo(LoginRequest request) {
        Map<String, Object> userInfo = new HashMap<>();
        if (StrUtil.isNotBlank(request.getNickname())) {
            userInfo.put("nickname", request.getNickname());
        }
        if (StrUtil.isNotBlank(request.getHeadPic())) {
            userInfo.put("headPic", request.getHeadPic());
        }
        if (StrUtil.isNotBlank(request.getSex())) {
            userInfo.put("sex", request.getSex());
        }
        return userInfo;
    }

    private User autoRegisterUser(LoginRequest request) {
        // 实现自动注册逻辑
        // 这里需要根据具体业务需求实现
        return null;
    }

    private String generateCaptcha() {
        return String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
    }

    private boolean sendEmailCaptcha(String email, String captcha, String type) {
        // 实现邮箱验证码发送
        // 这里需要集成邮件服务
        return true;
    }

    private boolean sendSmsCaptcha(String phone, String captcha, String type) {
        // 实现短信验证码发送
        // 这里需要集成短信服务
        return true;
    }

    @Override
    public AuthResult weixinLogin(String accessToken, String openId, Map<String, Object> userInfo) {
        // 实现微信登录逻辑
        // 这里需要根据具体的微信登录实现
        return AuthResult.failure("NOT_IMPLEMENTED", "微信登录功能待实现");
    }

    @Override
    public AuthResult qqLogin(String accessToken, String openId, Map<String, Object> userInfo) {
        // 实现QQ登录逻辑
        return AuthResult.failure("NOT_IMPLEMENTED", "QQ登录功能待实现");
    }

    @Override
    public AuthResult appleIdLogin(String jwtToken, Map<String, Object> userInfo) {
        // 实现Apple ID登录逻辑
        return AuthResult.failure("NOT_IMPLEMENTED", "Apple ID登录功能待实现");
    }

    @Override
    public AuthResult register(LoginRequest request) {
        // 实现用户注册逻辑
        return AuthResult.failure("NOT_IMPLEMENTED", "注册功能待实现");
    }

    @Override
    public boolean accountExists(String account) {
        return getUserByAccount(account) != null;
    }

    @Override
    public User getUserByAccount(String account) {
        try {
            // 先从缓存获取 (暂时跳过缓存，直接查数据库)
            // User user = cacheService.getUserByAccount(account);
            // if (user != null) {
            //     return user;
            // }

            // 缓存未命中，从数据库获取
            User user = null;
            UserExample example = new UserExample();
            UserExample.Criteria criteria = example.createCriteria();

            if (account.contains("@")) {
                criteria.andEmailEqualTo(account);
            } else if (account.matches("^1[3-9]\\d{9}$")) {
                criteria.andPhoneEqualTo(account);
            } else {
                criteria.andUsernameEqualTo(account);
            }

            List<User> users = userMapper.selectByExample(example);
            if (!users.isEmpty()) {
                user = users.get(0);
                // 检查账号状态：只返回正常状态的用户
                if (user.getAccountStatus() != null && user.getAccountStatus() != 1) {
                    user = null; // 账号状态异常，返回null
                }
            }

            if (user != null) {
                cacheService.cacheUserInfo(user);
            }

            return user;

        } catch (Exception e) {
            log.error("根据账号获取用户失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> checkLoginSecurity(LoginRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查账号是否被锁定
            if (securityService.isAccountLocked(request.getAccount())) {
                result.put("allowed", false);
                result.put("errorCode", "ACCOUNT_LOCKED");
                result.put("errorMessage", "账号已被锁定");
                return result;
            }

            // 检查IP是否被限制
            if (securityService.isIpBlocked(request.getClientIp())) {
                result.put("allowed", false);
                result.put("errorCode", "IP_BLOCKED");
                result.put("errorMessage", "IP地址已被限制");
                return result;
            }

            // 检查登录失败次数
            int failureCount = getLoginFailureCount(request.getAccount(), request.getClientIp());
            if (failureCount >= 5) {
                result.put("allowed", false);
                result.put("errorCode", "TOO_MANY_FAILURES");
                result.put("errorMessage", "登录失败次数过多，请稍后再试");
                return result;
            }

            // 检查是否需要验证码
            if (failureCount >= 3 && StrUtil.isBlank(request.getImageCaptcha())) {
                result.put("allowed", false);
                result.put("errorCode", "NEED_CAPTCHA");
                result.put("errorMessage", "需要图形验证码");
                return result;
            }

            // 验证图形验证码
            if (StrUtil.isNotBlank(request.getImageCaptcha())) {
                if (!securityService.validateImageCaptcha(request.getImageCaptchaKey(), request.getImageCaptcha())) {
                    result.put("allowed", false);
                    result.put("errorCode", "CAPTCHA_ERROR");
                    result.put("errorMessage", "图形验证码错误");
                    return result;
                }
            }

            result.put("allowed", true);
            return result;

        } catch (Exception e) {
            log.error("安全检查失败: {}", e.getMessage(), e);
            result.put("allowed", false);
            result.put("errorCode", "SECURITY_CHECK_FAILED");
            result.put("errorMessage", "安全检查失败");
            return result;
        }
    }

    @Override
    public void recordLoginHistory(Long userId, LoginRequest request, boolean success) {
        try {
            securityService.recordLoginHistory(userId, request.getAccount(),
                    request.getLoginType(), request.getClientIp(),
                    request.getUserAgent(), success);
        } catch (Exception e) {
            log.error("记录登录历史失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public int getLoginFailureCount(String account, String clientIp) {
        try {
            return securityService.getLoginFailureCount(account, clientIp);
        } catch (Exception e) {
            log.error("获取登录失败次数失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public List<String> getSupportedLoginTypes() {
        return Arrays.asList(
                LoginWayConstant.LOGIN_BY_EMAIL_PASSWORD,
                LoginWayConstant.LOGIN_BY_EMAIL,
                LoginWayConstant.LOGIN_BY_MOBILE_PASSWORD,
                LoginWayConstant.LOGIN_BY_SMS,
                LoginWayConstant.LOGIN_BY_WEIXIN,
                LoginWayConstant.LOGIN_BY_QQ,
                LoginWayConstant.LOGIN_BY_APPLE_ID
        );
    }

    @Override
    public Map<String, Object> getAuthStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("supportedLoginTypes", getSupportedLoginTypes());
        stats.put("timestamp", new Date());
        stats.put("version", "2.0-unified");
        return stats;
    }

    @Override
    public Map<String, Object> healthCheck() {
        Map<String, Object> health = new HashMap<>();

        try {
            // 检查数据库连接 - 简化检查
            boolean dbHealth = userMapper != null;
            health.put("database", dbHealth);

            // 检查缓存服务 - 简化检查
            boolean cacheHealth = cacheService != null;
            health.put("cache", cacheHealth);

            // 检查Token服务 - 简化检查
            boolean tokenHealth = tokenProvider != null;
            health.put("token", tokenHealth);

            // 整体健康状态
            boolean overall = dbHealth && cacheHealth && tokenHealth;
            health.put("status", overall ? "UP" : "DOWN");
            health.put("timestamp", new Date());

        } catch (Exception e) {
            log.error("健康检查失败: {}", e.getMessage(), e);
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", new Date());
        }

        return health;
    }

    // 其他未实现的方法使用默认实现
    @Override
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        return false; // 待实现
    }

    @Override
    public boolean resetPassword(String account, String captcha, String newPassword) {
        return false; // 待实现
    }

    @Override
    public boolean bindThirdPartyAccount(Long userId, String loginType, String openId, Map<String, Object> userInfo) {
        return false; // 待实现
    }

    @Override
    public boolean unbindThirdPartyAccount(Long userId, String loginType) {
        return false; // 待实现
    }

    @Override
    public boolean lockAccount(String account, String reason, long duration) {
        return false; // 待实现
    }

    @Override
    public boolean unlockAccount(String account) {
        return false; // 待实现
    }

    @Override
    public List<Map<String, Object>> getUserActiveSessions(Long userId) {
        return new ArrayList<>(); // 待实现
    }

    @Override
    public boolean kickUserSession(Long userId, String sessionId) {
        return false; // 待实现
    }

    @Override
    public int cleanExpiredSessions() {
        return 0; // 待实现
    }
}
