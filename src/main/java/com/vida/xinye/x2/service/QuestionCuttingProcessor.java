package com.vida.xinye.x2.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.vida.xinye.x2.adapter.QuestionCuttingAdapter;
import com.vida.xinye.x2.config.QuestionCuttingConfig;
import com.vida.xinye.x2.constant.QuestionCuttingConstant;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyRequestDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyResponseDto;
import com.vida.xinye.x2.dto.param.QuestionCuttingRequest;
import com.vida.xinye.x2.enums.QuestionCuttingEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 智能切图统一处理器
 * 简化原有的服务层和策略管理器
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
@Slf4j
@Component
public class QuestionCuttingProcessor {

    @Autowired
    private List<QuestionCuttingAdapter> adapters;

    @Autowired
    private QuestionCuttingConfig config;

    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    /**
     * 统一的切图处理入口
     */
    public QuestionCuttingResultDto process(QuestionCuttingRequest request) {
        String requestId = IdUtil.simpleUUID();
        log.info("开始处理切图请求 - ID: {}, 策略: {}, 提供商: {}", 
                requestId, request.getStrategy(), request.getProvider());

        try {
            // 参数验证和预处理
            validateAndPreprocess(request);

            // 构建内部请求
            ThirdPartyRequestDto internalRequest = buildInternalRequest(request, requestId);

            // 执行切图策略
            QuestionCuttingResultDto result = executeStrategy(internalRequest, request);
            
            result.setRequestId(requestId);
            result.setCreateTime(new Date());

            log.info("切图处理完成 - ID: {}, 成功: {}, 题目数: {}", 
                    requestId, result.getSuccess(), 
                    result.getQuestions() != null ? result.getQuestions().size() : 0);

            return result;

        } catch (Exception e) {
            log.error("切图处理失败 - ID: {}, 错误: {}", requestId, e.getMessage(), e);
            return buildErrorResult(requestId, e);
        }
    }

    /**
     * 执行切图策略
     */
    private QuestionCuttingResultDto executeStrategy(ThirdPartyRequestDto request, QuestionCuttingRequest originalRequest) {
        String strategy = getEffectiveStrategy(originalRequest.getStrategy());
        
        switch (strategy) {
            case QuestionCuttingConstant.Strategy.SPECIFIED:
                return executeSpecified(request, originalRequest.getProvider());
            
            case QuestionCuttingConstant.Strategy.BEST_QUALITY:
                return executeBestQuality(request);
            
            case QuestionCuttingConstant.Strategy.FASTEST:
                return executeFastest(request);
            
            case QuestionCuttingConstant.Strategy.AGGREGATED:
                return executeAggregated(request);
            
            case QuestionCuttingConstant.Strategy.ROUND_ROBIN:
                return executeRoundRobin(request);
            
            default:
                log.warn("未知策略: {}, 使用最佳质量策略", strategy);
                return executeBestQuality(request);
        }
    }

    /**
     * 指定提供商策略
     */
    private QuestionCuttingResultDto executeSpecified(ThirdPartyRequestDto request, String provider) {
        String effectiveProvider = getEffectiveProvider(provider);
        
        QuestionCuttingAdapter adapter = findAdapter(effectiveProvider);
        if (adapter == null) {
            log.warn("指定的提供商不可用: {}, 使用最佳质量策略", effectiveProvider);
            return executeBestQuality(request);
        }
        
        return executeWithAdapter(request, adapter);
    }

    /**
     * 最佳质量策略
     */
    private QuestionCuttingResultDto executeBestQuality(ThirdPartyRequestDto request) {
        List<QuestionCuttingAdapter> availableAdapters = getAvailableAdapters();
        if (availableAdapters.isEmpty()) {
            return buildErrorResult(request.getRequestId(), new RuntimeException("没有可用的适配器"));
        }

        // 按质量评分排序，选择最高的
        QuestionCuttingAdapter bestAdapter = availableAdapters.stream()
                .max(Comparator.comparingInt(QuestionCuttingAdapter::getQualityScore))
                .orElse(availableAdapters.get(0));

        return executeWithAdapter(request, bestAdapter);
    }

    /**
     * 最快速度策略
     */
    private QuestionCuttingResultDto executeFastest(ThirdPartyRequestDto request) {
        List<QuestionCuttingAdapter> availableAdapters = getAvailableAdapters();
        if (availableAdapters.isEmpty()) {
            return buildErrorResult(request.getRequestId(), new RuntimeException("没有可用的适配器"));
        }

        // 按响应时间排序，选择最快的
        QuestionCuttingAdapter fastestAdapter = availableAdapters.stream()
                .min(Comparator.comparingLong(QuestionCuttingAdapter::getAverageResponseTime))
                .orElse(availableAdapters.get(0));

        return executeWithAdapter(request, fastestAdapter);
    }

    /**
     * 聚合策略 - 并行调用多个接口，选择最佳结果
     */
    private QuestionCuttingResultDto executeAggregated(ThirdPartyRequestDto request) {
        List<QuestionCuttingAdapter> availableAdapters = getAvailableAdapters();
        if (availableAdapters.isEmpty()) {
            return buildErrorResult(request.getRequestId(), new RuntimeException("没有可用的适配器"));
        }

        // 并行调用所有可用适配器
        List<CompletableFuture<QuestionCuttingResultDto>> futures = availableAdapters.stream()
                .map(adapter -> CompletableFuture.supplyAsync(() -> 
                    executeWithAdapter(request, adapter), executorService))
                .collect(Collectors.toList());

        try {
            // 等待所有结果
            List<QuestionCuttingResultDto> results = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0]))
                    .thenApply(v -> futures.stream()
                            .map(CompletableFuture::join)
                            .filter(Objects::nonNull)
                            .filter(QuestionCuttingResultDto::getSuccess)
                            .collect(Collectors.toList()))
                    .get();

            if (results.isEmpty()) {
                return buildErrorResult(request.getRequestId(), new RuntimeException("所有适配器都失败了"));
            }

            // 选择质量最好的结果
            return results.stream()
                    .max(Comparator.comparing(r -> r.getConfidence() != null ? r.getConfidence() : 0.0))
                    .orElse(results.get(0));

        } catch (Exception e) {
            log.error("聚合策略执行失败: {}", e.getMessage(), e);
            return buildErrorResult(request.getRequestId(), e);
        }
    }

    /**
     * 轮询策略
     */
    private QuestionCuttingResultDto executeRoundRobin(ThirdPartyRequestDto request) {
        List<QuestionCuttingAdapter> availableAdapters = getAvailableAdapters();
        if (availableAdapters.isEmpty()) {
            return buildErrorResult(request.getRequestId(), new RuntimeException("没有可用的适配器"));
        }

        // 简单的轮询实现（基于时间戳）
        int index = (int) (System.currentTimeMillis() % availableAdapters.size());
        QuestionCuttingAdapter selectedAdapter = availableAdapters.get(index);

        return executeWithAdapter(request, selectedAdapter);
    }

    /**
     * 使用指定适配器执行切图
     */
    private QuestionCuttingResultDto executeWithAdapter(ThirdPartyRequestDto request, QuestionCuttingAdapter adapter) {
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("使用适配器执行切图 - 请求ID: {}, 提供商: {}", 
                    request.getRequestId(), adapter.getProvider().getCode());

            // 调用第三方API
            ThirdPartyResponseDto response = adapter.callThirdPartyApi(request);
            
            // 转换为标准格式
            QuestionCuttingResultDto result = adapter.convertToStandardResult(response, request.getRequestId());
            
            long processingTime = System.currentTimeMillis() - startTime;
            result.setProcessingTime(processingTime);
            result.setProvider(adapter.getProvider().getCode());

            return result;

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("适配器执行失败 - 请求ID: {}, 提供商: {}, 时间: {}ms, 错误: {}", 
                     request.getRequestId(), adapter.getProvider().getCode(), processingTime, e.getMessage(), e);
            
            QuestionCuttingResultDto errorResult = buildErrorResult(request.getRequestId(), e);
            errorResult.setProcessingTime(processingTime);
            errorResult.setProvider(adapter.getProvider().getCode());
            return errorResult;
        }
    }

    // 辅助方法
    private void validateAndPreprocess(QuestionCuttingRequest request) {
        if (request.getImageFile() == null && StrUtil.isBlank(request.getImageUrl()) && StrUtil.isBlank(request.getImageBase64())) {
            throw new IllegalArgumentException("必须提供图片文件、URL或Base64编码中的一种");
        }
    }

    private ThirdPartyRequestDto buildInternalRequest(QuestionCuttingRequest request, String requestId) {
        ThirdPartyRequestDto internalRequest = new ThirdPartyRequestDto();
        internalRequest.setRequestId(requestId);
        internalRequest.setImageUrl(request.getImageUrl());
        internalRequest.setImageBase64(request.getImageBase64());
        internalRequest.setSubject(request.getSubject());
        internalRequest.setGrade(request.getGrade());
        internalRequest.setTimeoutSeconds(request.getTimeoutSeconds());
        
        // 处理文件上传
        if (request.getImageFile() != null) {
            try {
                byte[] imageBytes = request.getImageFile().getBytes();
                String base64 = Base64.getEncoder().encodeToString(imageBytes);
                internalRequest.setImageBase64(base64);
            } catch (Exception e) {
                throw new RuntimeException("文件处理失败: " + e.getMessage(), e);
            }
        }
        
        return internalRequest;
    }

    private String getEffectiveStrategy(String strategy) {
        if (StrUtil.isNotBlank(strategy)) {
            return strategy;
        }
        return config.getCommon() != null ? 
               config.getCommon().getDefaultStrategy() : 
               QuestionCuttingConstant.Default.DEFAULT_STRATEGY;
    }

    private String getEffectiveProvider(String provider) {
        if (StrUtil.isNotBlank(provider)) {
            return provider;
        }
        return config.getCommon() != null ? 
               config.getCommon().getDefaultProvider() : 
               QuestionCuttingConstant.Default.DEFAULT_PROVIDER;
    }

    private QuestionCuttingAdapter findAdapter(String provider) {
        return adapters.stream()
                .filter(adapter -> adapter.getProvider().getCode().equals(provider))
                .filter(QuestionCuttingAdapter::isAvailable)
                .findFirst()
                .orElse(null);
    }

    private List<QuestionCuttingAdapter> getAvailableAdapters() {
        return adapters.stream()
                .filter(QuestionCuttingAdapter::isAvailable)
                .sorted(Comparator.comparingInt(QuestionCuttingAdapter::getPriority))
                .collect(Collectors.toList());
    }

    private QuestionCuttingResultDto buildErrorResult(String requestId, Exception e) {
        QuestionCuttingResultDto result = new QuestionCuttingResultDto();
        result.setRequestId(requestId);
        result.setSuccess(false);
        result.setErrorCode(QuestionCuttingConstant.ErrorCode.INTERNAL_ERROR);
        result.setErrorMessage(e.getMessage());
        result.setCreateTime(new Date());
        return result;
    }

    public List<String> getSupportedProviders() {
        return adapters.stream()
                .map(adapter -> adapter.getProvider().getCode())
                .collect(Collectors.toList());
    }
}
