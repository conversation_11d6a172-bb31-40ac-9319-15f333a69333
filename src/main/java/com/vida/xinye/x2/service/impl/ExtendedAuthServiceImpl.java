package com.vida.xinye.x2.service.impl;

import cn.hutool.core.util.StrUtil;
import com.vida.xinye.x2.component.JwtTokenProvider;
import com.vida.xinye.x2.constant.AccountTypeEnum;
import com.vida.xinye.x2.constant.LoginWayConstant;
import com.vida.xinye.x2.dto.TokenDto;
import com.vida.xinye.x2.dto.param.*;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.UserLoginHistoryMapper;
import com.vida.xinye.x2.mbg.mapper.UserMapper;
import com.vida.xinye.x2.mbg.mapper.UserThirdPartyAuthMapper;
import com.vida.xinye.x2.mbg.model.User;
import com.vida.xinye.x2.mbg.model.UserExample;
import com.vida.xinye.x2.mbg.model.UserLoginHistory;
import com.vida.xinye.x2.mbg.model.UserThirdPartyAuth;
import com.vida.xinye.x2.service.CaptchaService;
import com.vida.xinye.x2.service.ExtendedAuthService;
import com.vida.xinye.x2.service.RedisService;
import com.vida.xinye.x2.validator.AccountSecurityValidator;
import com.vida.xinye.x2.validator.PasswordStrengthValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 扩展认证服务实现（参考snapTag项目架构）
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
@Slf4j
public class ExtendedAuthServiceImpl implements ExtendedAuthService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserThirdPartyAuthMapper userThirdPartyAuthMapper;

    @Autowired
    private UserCacheServiceImpl userCacheService;

    @Autowired
    private UserLoginHistoryMapper userLoginHistoryMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private CaptchaService captchaService;

    // @Autowired
    // private ThirdPartyLoginService thirdPartyLoginService; // 简化版不再使用

    @Autowired
    private AccountSecurityValidator accountSecurityValidator;

    @Autowired
    private PasswordStrengthValidator passwordStrengthValidator;

    @Autowired
    private RedisService redisService;

    @Autowired
    private LoginSecurityServiceImpl loginSecurityService;

    @Override
    public TokenDto emailLogin(EmailLoginDto emailLoginDto) {
        try {
            // 1. 验证邮箱格式
            if (!accountSecurityValidator.isValidEmail(emailLoginDto.getEmail())) {
                Asserts.fail("邮箱格式不正确");
            }

            // 2. 检查账号是否被锁定
            String loginWay = "password".equals(emailLoginDto.getLoginMethod()) ?
                    LoginWayConstant.LOGIN_BY_EMAIL_PASSWORD : LoginWayConstant.LOGIN_BY_EMAIL;
            checkAccountSecurity(emailLoginDto.getEmail(), loginWay);

            // 3. 查找用户
            User user = findUserByEmail(emailLoginDto.getEmail());

            // 如果用户不存在，则自动注册（支持验证码和密码登录）
            if (user == null && "captcha".equals(emailLoginDto.getLoginMethod())) {
                log.info("邮箱验证码登录-用户不存在，自动注册: {}", emailLoginDto.getEmail());
                user = autoRegisterByEmail(emailLoginDto.getEmail());
            } else if (user == null && "password".equals(emailLoginDto.getLoginMethod())) {
                log.info("邮箱密码登录-用户不存在，自动注册: {}", emailLoginDto.getEmail());
                user = autoRegisterByEmailWithPassword(emailLoginDto.getEmail(), emailLoginDto.getPassword());
            } else if (user == null) {
                // 其他情况用户不存在，报错
                recordLoginFailure(emailLoginDto.getEmail(), LoginWayConstant.LOGIN_BY_EMAIL);
                Asserts.fail("邮箱未注册");
            }

            // 4. 验证登录方式
            if ("password".equals(emailLoginDto.getLoginMethod())) {
                // 密码登录
                if (StrUtil.isEmpty(emailLoginDto.getPassword())) {
                    Asserts.fail("密码不能为空");
                }
                // 如果用户是刚注册的（通过autoRegisterByEmailWithPassword），则跳过密码验证
                // 否则验证密码
                if (user.getPassword() != null && !passwordEncoder.matches(emailLoginDto.getPassword(), user.getPassword())) {
                    recordLoginFailure(emailLoginDto.getEmail(), LoginWayConstant.LOGIN_BY_EMAIL_PASSWORD);
                    Asserts.fail("密码错误");
                }
            } else if ("captcha".equals(emailLoginDto.getLoginMethod())) {
                // 验证码登录
                if (StrUtil.isEmpty(emailLoginDto.getCaptcha())) {
                    Asserts.fail("验证码不能为空");
                }
                if (!captchaService.verifyCode(emailLoginDto.getEmail(), emailLoginDto.getCaptcha())) {
                    Asserts.fail("验证码错误或已过期");
                }
            } else {
                Asserts.fail("不支持的登录方式");
            }

            // 5. 登录成功，清除失败记录
            loginSecurityService.clearLoginFailures(emailLoginDto.getEmail(), loginWay);

            // 6. 更新登录时间
            updateLastLoginTime(user.getId());

            // 7. 记录登录历史
            recordLoginHistory(user.getId(), loginWay, LoginWayConstant.LOGIN_SUCCESS);

            // 8. 缓存用户信息
            userCacheService.cacheUserInfo(user);

            // 9. 生成Token
            TokenDto tokenDto = createTokenDto(user);
            tokenDto.setLoginWay(loginWay);

            // 10. 缓存登录Token
            userCacheService.cacheLoginToken(user.getId(), tokenDto.getAccessToken(), null);

            return tokenDto;

        } catch (Exception e) {
            log.error("邮箱登录失败", e);
            throw e;
        }
    }

    @Override
    public TokenDto phonePasswordLogin(PhonePasswordLoginDto phonePasswordLoginDto) {
        try {
            // 1. 验证手机号格式
            if (!accountSecurityValidator.isValidPhone(phonePasswordLoginDto.getPhone())) {
                Asserts.fail("手机号格式不正确");
            }

            // 2. 检查账号安全
            checkAccountSecurity(phonePasswordLoginDto.getPhone(), LoginWayConstant.LOGIN_BY_MOBILE_PASSWORD);

            // 3. 查找用户
            User user = findUserByPhone(phonePasswordLoginDto.getPhone());
            if (user == null) {
                recordLoginFailure(phonePasswordLoginDto.getPhone(), LoginWayConstant.LOGIN_BY_MOBILE_PASSWORD);
                Asserts.fail("手机号未注册");
            }

            // 4. 验证密码
            if (!passwordEncoder.matches(phonePasswordLoginDto.getPassword(), user.getPassword())) {
                recordLoginFailure(phonePasswordLoginDto.getPhone(), LoginWayConstant.LOGIN_BY_MOBILE_PASSWORD);
                Asserts.fail("密码错误");
            }

            // 5. 登录成功处理
            loginSecurityService.clearLoginFailures(phonePasswordLoginDto.getPhone(), LoginWayConstant.LOGIN_BY_MOBILE_PASSWORD);
            updateLastLoginTime(user.getId());
            recordLoginHistory(user.getId(), LoginWayConstant.LOGIN_BY_MOBILE_PASSWORD, LoginWayConstant.LOGIN_SUCCESS);

            // 6. 缓存用户信息
            userCacheService.cacheUserInfo(user);

            // 7. 生成Token
            TokenDto tokenDto = createTokenDto(user);
            tokenDto.setLoginWay(LoginWayConstant.LOGIN_BY_MOBILE_PASSWORD);

            // 8. 缓存登录Token
            userCacheService.cacheLoginToken(user.getId(), tokenDto.getAccessToken(), null);

            return tokenDto;

        } catch (Exception e) {
            log.error("手机号密码登录失败", e);
            throw e;
        }
    }

    @Override
    public TokenDto phoneLogin(PhoneLoginDto phoneLoginDto) {
        try {
            // 1. 验证手机号格式
            if (!accountSecurityValidator.isValidPhone(phoneLoginDto.getPhone())) {
                Asserts.fail("手机号格式不正确");
            }

            // 2. 根据登录方式进行不同的验证
            String loginWay;
            User user;

            if ("password".equals(phoneLoginDto.getLoginMethod())) {
                // 密码登录
                if (StrUtil.isEmpty(phoneLoginDto.getPassword())) {
                    Asserts.fail("密码不能为空");
                }

                loginWay = LoginWayConstant.LOGIN_BY_MOBILE_PASSWORD;
                checkAccountSecurity(phoneLoginDto.getPhone(), loginWay);

                user = findUserByPhone(phoneLoginDto.getPhone());
                if (user == null) {
                    // 手机号密码登录支持自动注册
                    log.info("手机号密码登录-用户不存在，自动注册: {}", phoneLoginDto.getPhone());
                    user = autoRegisterByPhoneWithPassword(phoneLoginDto.getPhone(), phoneLoginDto.getPassword());
                } else {
                    // 验证密码
                    if (StrUtil.isEmpty(user.getPassword()) ||
                            !passwordEncoder.matches(phoneLoginDto.getPassword(), user.getPassword())) {
                        recordLoginFailure(phoneLoginDto.getPhone(), loginWay);
                        Asserts.fail("密码错误");
                    }
                }

            } else if ("captcha".equals(phoneLoginDto.getLoginMethod())) {
                // 验证码登录
                if (StrUtil.isEmpty(phoneLoginDto.getCaptcha())) {
                    Asserts.fail("验证码不能为空");
                }

                loginWay = LoginWayConstant.LOGIN_BY_SMS;
                checkAccountSecurity(phoneLoginDto.getPhone(), loginWay);

                // 验证短信验证码
                if (!captchaService.verifyCode(phoneLoginDto.getPhone(), phoneLoginDto.getCaptcha())) {
                    recordLoginFailure(phoneLoginDto.getPhone(), loginWay);
                    Asserts.fail("验证码错误或已过期");
                }

                // 查找用户，如果不存在则自动注册（保持与原有SMS登录逻辑一致）
                user = findUserByPhone(phoneLoginDto.getPhone());
                if (user == null) {
                    user = autoRegisterByPhone(phoneLoginDto.getPhone());
                }

            } else {
                Asserts.fail("不支持的登录方式");
                return null; // 这行不会执行，但为了编译通过
            }

            // 3. 生成Token
            TokenDto tokenDto = generateTokenForUser(user, loginWay);

            // 4. 记录登录成功
            recordLoginSuccess(phoneLoginDto.getPhone(), loginWay);

            // 5. 更新用户最后登录时间
            updateUserLastLoginTime(user.getId());

            log.info("手机号登录成功 - 手机号: {}, 登录方式: {}, 用户ID: {}",
                    phoneLoginDto.getPhone(), phoneLoginDto.getLoginMethod(), user.getId());

            return tokenDto;

        } catch (Exception e) {
            log.error("手机号登录失败 - 手机号: {}, 登录方式: {}",
                    phoneLoginDto.getPhone(), phoneLoginDto.getLoginMethod(), e);
            throw e;
        }
    }

    /**
     * 通过手机号自动注册用户（验证码登录时使用）
     */
    private User autoRegisterByPhone(String phone) {
        User user = new User();
        user.setPhone(phone);
        user.setUsername(phone); // 使用手机号作为用户名
        user.setNickname("用户" + phone.substring(phone.length() - 4)); // 使用手机号后4位作为昵称
        user.setPhoneVerified(1); // 通过验证码登录，认为手机号已验证
        user.setRequirePhoneBinding(0); // 不需要绑定手机号
        user.setAccountStatus(1); // 正常状态
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());

        userMapper.insertSelective(user);

        log.info("手机号验证码登录自动注册用户 - 手机号: {}, 用户ID: {}", phone, user.getId());

        return user;
    }

    /**
     * 通过手机号和密码自动注册用户（密码登录时使用）
     */
    private User autoRegisterByPhoneWithPassword(String phone, String password) {
        User user = new User();
        user.setPhone(phone);
        user.setUsername(phone); // 使用手机号作为用户名
        user.setPassword(passwordEncoder.encode(password)); // 加密密码
        user.setNickname("用户" + phone.substring(phone.length() - 4)); // 使用手机号后4位作为昵称
        user.setPhoneVerified(0); // 密码登录时手机号未验证，需要后续验证
        user.setRequirePhoneBinding(0); // 不需要绑定手机号（已经是手机号注册）
        user.setAccountStatus(1); // 正常状态
        user.setEmailVerified(0);
        user.setLoginCount(0);

        Date now = new Date();
        user.setCreateTime(now);
        user.setUpdateTime(now);
        user.setLastLoginTime(now);

        userMapper.insertSelective(user);

        log.info("手机号密码登录自动注册用户 - 手机号: {}, 用户ID: {}", phone, user.getId());

        return user;
    }

    /**
     * 通过邮箱自动注册用户（验证码登录时使用）
     */
    private User autoRegisterByEmail(String email) {
        User user = new User();
        user.setEmail(email);
        user.setUsername(email); // 使用邮箱作为用户名
        user.setNickname("用户" + email.substring(0, email.indexOf("@"))); // 使用邮箱前缀作为昵称
        user.setEmailVerified(1); // 通过验证码登录，认为邮箱已验证
        user.setRequirePhoneBinding(1); // 需要绑定手机号
        user.setAccountStatus(1); // 正常状态
        user.setPhoneVerified(0);
        user.setLoginCount(0);

        Date now = new Date();
        user.setCreateTime(now);
        user.setUpdateTime(now);
        user.setLastLoginTime(now);

        userMapper.insertSelective(user);

        log.info("邮箱验证码登录自动注册用户 - 邮箱: {}, 用户ID: {}", email, user.getId());

        return user;
    }

    /**
     * 通过邮箱和密码自动注册用户（密码登录时使用）
     */
    private User autoRegisterByEmailWithPassword(String email, String password) {
        User user = new User();
        user.setEmail(email);
        user.setUsername(email); // 使用邮箱作为用户名
        user.setPassword(passwordEncoder.encode(password)); // 加密密码
        user.setNickname("用户" + email.substring(0, email.indexOf("@"))); // 使用邮箱前缀作为昵称
        user.setEmailVerified(0); // 密码登录时邮箱未验证，需要后续验证
        user.setRequirePhoneBinding(1); // 需要绑定手机号
        user.setAccountStatus(1); // 正常状态
        user.setPhoneVerified(0);
        user.setLoginCount(0);

        Date now = new Date();
        user.setCreateTime(now);
        user.setUpdateTime(now);
        user.setLastLoginTime(now);

        userMapper.insertSelective(user);

        log.info("邮箱密码登录自动注册用户 - 邮箱: {}, 用户ID: {}", email, user.getId());

        return user;
    }

    @Override
    @Transactional
    public TokenDto thirdPartyLogin(ThirdPartyLoginDto loginDto) {
        try {
            log.info("第三方登录开始 - 登录方式: {}, openId: {}", loginDto.getLoginWay(), loginDto.getOpenId());

            // 1. 基本参数校验（参考其他项目的校验逻辑）
            if (loginDto.getLoginWay() == null || loginDto.getOpenId() == null) {
                throw new IllegalArgumentException("登录方式和openId不能为空");
            }

            // 2. 第三方登录必须有accessToken（所有第三方登录都需要）
            if (loginDto.getAccessToken() == null || loginDto.getAccessToken().trim().isEmpty()) {
                throw new IllegalArgumentException("第三方登录accessToken不能为空");
            }

            // 3. 检查账号是否已注销（参考其他项目的实现）
            String account = getAccountFromLoginDto(loginDto);
            if (account != null && userCacheService.checkIsUnRegister(account)) {
                throw new IllegalArgumentException("当前账号已注销，7天内无法再次注册");
            }

            // 4. 查找现有用户（简化版）
            User user = findUserByThirdPartySimple(loginDto);

            if (user == null) {
                // 5. 用户不存在，创建新用户（参考其他项目的注册逻辑）
                user = createUserForThirdParty(loginDto);
                saveThirdPartyAuthSimple(user.getId(), loginDto);
                log.info("第三方登录-创建新用户: userId={}", user.getId());
            } else {
                // 6. 用户存在，更新登录信息
                updateThirdPartyAuthSimple(user.getId(), loginDto);
                log.info("第三方登录-用户已存在: userId={}", user.getId());
            }

            // 7. 更新最后登录时间
            updateLastLoginTime(user.getId());

            // 8. 记录登录历史
            recordLoginHistory(user.getId(), loginDto.getLoginWay(), LoginWayConstant.LOGIN_SUCCESS);

            // 9. 生成Token（参考其他项目的简单Token生成）
            TokenDto tokenDto = createTokenDto(user);
            tokenDto.setLoginWay(loginDto.getLoginWay());

            // 10. 检查是否需要绑定手机号
            if (user.getRequirePhoneBinding() != null && user.getRequirePhoneBinding() == 1) {
                tokenDto.setRequirePhoneBinding(true);
            }

            log.info("第三方登录成功 - 用户ID: {}, 登录方式: {}", user.getId(), loginDto.getLoginWay());
            return tokenDto;

        } catch (Exception e) {
            log.error("第三方登录失败 - 登录方式: {}, openId: {}", loginDto.getLoginWay(), loginDto.getOpenId(), e);
            throw e;
        }
    }

    /**
     * 简化版：查找第三方用户（参考其他项目）
     */
    private User findUserByThirdPartySimple(ThirdPartyLoginDto loginDto) {
        try {
            String provider = getProviderFromLoginWay(loginDto.getLoginWay());
            if (provider == null) {
                return null;
            }

            // 优先通过unionId查找
            if (loginDto.getUnionId() != null && !loginDto.getUnionId().isEmpty()) {
                UserThirdPartyAuth auth = userThirdPartyAuthMapper.selectByProviderAndUnionId(provider, loginDto.getUnionId());
                if (auth != null) {
                    return userMapper.selectByPrimaryKey(auth.getUserId());
                }
            }

            // 通过openId查找
            UserThirdPartyAuth auth = userThirdPartyAuthMapper.selectByProviderAndOpenId(provider, loginDto.getOpenId());
            if (auth != null) {
                return userMapper.selectByPrimaryKey(auth.getUserId());
            }

            return null;
        } catch (Exception e) {
            log.error("查找第三方用户异常", e);
            return null;
        }
    }

    /**
     * 简化版：为第三方登录创建用户（参考其他项目）
     */
    private User createUserForThirdParty(ThirdPartyLoginDto loginDto) {
        try {
            User user = new User();

            // 基本信息
            String nickname = loginDto.getNickname();
            if (nickname == null || nickname.isEmpty()) {
                nickname = "用户" + System.currentTimeMillis();
            }
            user.setNickname(nickname);
            user.setAvatar(loginDto.getHeadPic());

            // 设置username（第三方登录使用nickname作为username）
            user.setUsername(generateUsernameForThirdParty(loginDto, nickname));

            // 第三方登录用户默认设置
            user.setRequirePhoneBinding(1); // 需要绑定手机号
            user.setAccountStatus(1); // 正常状态
            user.setPhoneVerified(0);
            user.setEmailVerified(0);
            user.setLoginCount(0);

            // 时间字段
            Date now = new Date();
            user.setCreateTime(now);
            user.setUpdateTime(now);
            user.setLastLoginTime(now);

            userMapper.insertSelective(user);
            log.info("创建第三方登录用户成功: userId={}, username={}, nickname={}",
                   user.getId(), user.getUsername(), nickname);

            return user;
        } catch (Exception e) {
            log.error("创建第三方登录用户失败", e);
            throw new RuntimeException("创建用户失败", e);
        }
    }

    /**
     * 为第三方登录生成username
     */
    private String generateUsernameForThirdParty(ThirdPartyLoginDto loginDto, String nickname) {
        // 第三方登录：直接使用nickname作为username，简洁友好
        if (nickname != null && !nickname.trim().isEmpty()) {
            return nickname.trim();
        }

        // 兜底方案：如果nickname为空，使用默认格式
        return "用户" + System.currentTimeMillis();
    }

    /**
     * 简化版：保存第三方认证信息
     */
    private void saveThirdPartyAuthSimple(Long userId, ThirdPartyLoginDto loginDto) {
        try {
            UserThirdPartyAuth auth = new UserThirdPartyAuth();
            auth.setUserId(userId);
            auth.setProvider(getProviderFromLoginWay(loginDto.getLoginWay()));
            auth.setOpenId(loginDto.getOpenId());
            auth.setUnionId(loginDto.getUnionId());
            auth.setStatus(1);

            Date now = new Date();
            auth.setBindTime(now);
            auth.setLastLoginTime(now);
            auth.setCreateTime(now);
            auth.setUpdateTime(now);

            userThirdPartyAuthMapper.insertSelective(auth);
            log.info("保存第三方认证信息成功: userId={}, provider={}", userId, auth.getProvider());
        } catch (Exception e) {
            log.error("保存第三方认证信息失败", e);
        }
    }

    /**
     * 简化版：更新第三方认证信息
     */
    private void updateThirdPartyAuthSimple(Long userId, ThirdPartyLoginDto loginDto) {
        try {
            String provider = getProviderFromLoginWay(loginDto.getLoginWay());
            UserThirdPartyAuth auth = userThirdPartyAuthMapper.selectByUserIdAndProvider(userId, provider);

            if (auth != null) {
                auth.setLastLoginTime(new Date());
                auth.setUpdateTime(new Date());
                userThirdPartyAuthMapper.updateByPrimaryKeySelective(auth);
            }
        } catch (Exception e) {
            log.error("更新第三方认证信息失败", e);
        }
    }

    /**
     * 从登录DTO中提取账号信息（用于注销状态检查）
     * 与注销时的逻辑保持一致：openId_platform
     */
    private String getAccountFromLoginDto(ThirdPartyLoginDto loginDto) {
        // 与UserCacheServiceImpl中的generateThirdPartyAccount方法保持一致
        // 格式：{openId}_{platform}
        // 例如：**********715635713_weixin

        if (loginDto.getOpenId() == null || loginDto.getOpenId().trim().isEmpty()) {
            return null;
        }

        String openId = loginDto.getOpenId().trim();
        String platform = userCacheService.getPlatformFromLoginWay(loginDto.getLoginWay());

        if (platform == null) {
            return null;
        }

        // 组合成account：openId_platform（与注销时保持一致）
        String account = openId + "_" + platform;
        log.debug("生成第三方登录account: {}", account);

        return account;
    }



    @Override
    public boolean setPassword(Long userId, SetPasswordDto setPasswordDto) {
        try {
            // 1. 验证密码一致性
            if (!setPasswordDto.getNewPassword().equals(setPasswordDto.getConfirmPassword())) {
                Asserts.fail("两次输入的密码不一致");
            }

            // 2. 验证密码强度
            PasswordStrengthValidator.PasswordValidationResult result =
                    passwordStrengthValidator.validatePassword(setPasswordDto.getNewPassword());
            if (!result.isValid()) {
                Asserts.fail(result.getMessage());
            }

            // 3. 获取用户信息
            User user = userMapper.selectByPrimaryKey(userId);
            if (user == null) {
                Asserts.fail("用户不存在");
            }

            // 4. 如果是修改密码，验证旧密码
            if (StrUtil.isNotEmpty(setPasswordDto.getOldPassword())) {
                if (StrUtil.isEmpty(user.getPassword()) ||
                        !passwordEncoder.matches(setPasswordDto.getOldPassword(), user.getPassword())) {
                    Asserts.fail("原密码错误");
                }
            }

            // 5. 更新密码
            User updateUser = new User();
            updateUser.setId(userId);
            updateUser.setPassword(passwordEncoder.encode(setPasswordDto.getNewPassword()));
            updateUser.setUpdateTime(new Date());

            return userMapper.updateByPrimaryKeySelective(updateUser) > 0;

        } catch (Exception e) {
            log.error("设置密码失败", e);
            throw e;
        }
    }

    @Override
    public boolean forgotPassword(ResetPwdDto resetPwdDto) {
        // 复用现有的resetPwd逻辑
        try {
            // 验证验证码
            if (!captchaService.verifyCode(resetPwdDto.getUsername(), resetPwdDto.getCaptcha())) {
                Asserts.fail("验证码错误或已过期");
            }

            // 查找用户
            UserExample example = new UserExample();
            example.createCriteria().andUsernameEqualTo(resetPwdDto.getUsername());
            List<User> users = userMapper.selectByExample(example);
            if (users.isEmpty()) {
                Asserts.fail("账号不存在");
            }

            User user = users.get(0);
            user.setPassword(passwordEncoder.encode(resetPwdDto.getNewPassword()));
            user.setUpdateTime(new Date());

            return userMapper.updateByPrimaryKeySelective(user) > 0;

        } catch (Exception e) {
            log.error("忘记密码重置失败", e);
            throw e;
        }
    }

    @Override
    public boolean checkPhoneBindingRequired(Long userId) {
        User user = userMapper.selectByPrimaryKey(userId);
        if (user == null) {
            return false;
        }

        // 检查注册类型是否需要绑定手机号
        AccountTypeEnum accountType = AccountTypeEnum.getByValue(user.getRegisterType());
        if (accountType != null && accountType.requiresPhoneBinding()) {
            // 检查是否已绑定手机号
            return StrUtil.isEmpty(user.getPhone());
        }

        return false;
    }

    // 私有辅助方法
    private User findUserByEmail(String email) {
        UserExample example = new UserExample();
        example.createCriteria().andEmailEqualTo(email);
        return userMapper.selectByExample(example).stream().findFirst().orElse(null);
    }

    private User findUserByPhone(String phone) {
        UserExample example = new UserExample();
        example.createCriteria().andPhoneEqualTo(phone);
        return userMapper.selectByExample(example).stream().findFirst().orElse(null);
    }

    private void checkAccountSecurity(String account, String loginWay) {
        if (!loginSecurityService.canAttemptLogin(account, loginWay)) {
            String securityInfo = loginSecurityService.getSecurityInfo(account, loginWay);
            Asserts.fail(securityInfo);
        }
    }

    private void recordLoginFailure(String account, String loginWay) {
        loginSecurityService.recordLoginFailure(account, loginWay, "unknown");
    }

    private void updateLastLoginTime(Long userId) {
        User updateUser = new User();
        updateUser.setId(userId);
        updateUser.setLastLoginTime(new Date());
        userMapper.updateByPrimaryKeySelective(updateUser);
    }

    private void recordLoginHistory(Long userId, String loginWay, String status) {
        try {
            UserLoginHistory history = new UserLoginHistory();
            history.setUserId(userId);
            history.setLoginWay(loginWay);
            history.setLoginStatus(status);
            history.setLoginTime(new Date());
            history.setLoginIp("unknown"); // 这里可以从请求中获取真实IP
            history.setLoginDevice("unknown"); // 这里可以从User-Agent中解析设备信息
            history.setUserAgent("unknown"); // 这里可以从请求头中获取

            userLoginHistoryMapper.insertSelective(history);
            log.info("登录历史记录成功 - 用户ID: {}, 登录方式: {}, 状态: {}", userId, loginWay, status);
        } catch (Exception e) {
            log.error("记录登录历史失败 - 用户ID: {}, 登录方式: {}, 状态: {}", userId, loginWay, status, e);
            // 不抛出异常，避免影响登录流程
        }
    }

    private User findOrCreateUserByThirdParty(ThirdPartyLoginDto dto) {
        // 1. 根据第三方信息查找现有用户
        User existingUser = findUserByThirdPartyInfo(dto);
        if (existingUser != null) {
            return existingUser;
        }

        // 2. 创建新用户
        User newUser = new User();

        // 设置用户名（使用第三方平台+openId或unionId）
        String username = generateThirdPartyUsername(dto);
        newUser.setUsername(username);

        // 设置注册类型
        if (LoginWayConstant.LOGIN_BY_WEIXIN.equals(dto.getLoginWay())) {
            newUser.setRegisterType(AccountTypeEnum.WECHAT.getValue());
        } else if (LoginWayConstant.LOGIN_BY_QQ.equals(dto.getLoginWay())) {
            newUser.setRegisterType(AccountTypeEnum.QQ.getValue());
        } else if (LoginWayConstant.LOGIN_BY_SINA.equals(dto.getLoginWay())) {
            newUser.setRegisterType(AccountTypeEnum.WEIBO.getValue());
        } else if (LoginWayConstant.LOGIN_BY_APPLE_ID.equals(dto.getLoginWay())) {
            newUser.setRegisterType(AccountTypeEnum.APPLE.getValue());
        }

        // 设置昵称和头像
        if (StrUtil.isNotEmpty(dto.getNickname())) {
            newUser.setNickname(dto.getNickname());
        } else {
            // 设置默认昵称
            newUser.setNickname("用户" + System.currentTimeMillis());
        }

        if (StrUtil.isNotEmpty(dto.getHeadPic())) {
            newUser.setAvatar(dto.getHeadPic());
        }

        // 第三方登录用户需要绑定手机号
        newUser.setRequirePhoneBinding(1);

        // 设置其他默认值
        newUser.setAccountStatus(1); // 正常状态
        newUser.setParentMode(0); // 默认非家长模式
        newUser.setRoleType("1"); // 默认学生角色

        // 设置创建时间
        newUser.setCreateTime(new Date());
        newUser.setUpdateTime(new Date());

        // 插入数据库
        userMapper.insertSelective(newUser);

        log.info("创建第三方登录用户成功 - 用户名: {}, 登录方式: {}", username, dto.getLoginWay());

        return newUser;
    }

    private void saveOrUpdateThirdPartyAuth(Long userId, ThirdPartyLoginDto dto) {
        try {
            log.info("保存第三方认证信息 - 用户ID: {}, 登录方式: {}", userId, dto.getLoginWay());

            // 确定平台标识
            String provider = getProviderFromLoginWay(dto.getLoginWay());
            if (provider == null) {
                log.warn("不支持的登录方式: {}", dto.getLoginWay());
                return;
            }

            // 查找现有记录
            UserThirdPartyAuth auth = userThirdPartyAuthMapper.selectByUserIdAndProvider(userId, provider);
            if (auth == null) {
                auth = new UserThirdPartyAuth();
                auth.setUserId(userId);
                auth.setProvider(provider);
                auth.setBindTime(new Date());
                auth.setStatus(1);
                auth.setCreateTime(new Date());
            }

            // 设置统一字段
            auth.setOpenId(dto.getOpenId());
            auth.setUnionId(dto.getUnionId());
            auth.setLastLoginTime(new Date());
            auth.setUpdateTime(new Date());

            // 注意：用户信息（昵称、头像等）应该更新到user表中，而不是存储在这里

            // 保存或更新
            if (auth.getId() == null) {
                userThirdPartyAuthMapper.insertSelective(auth);
                log.info("新增第三方认证信息成功 - 用户ID: {}, 平台: {}", userId, provider);
            } else {
                userThirdPartyAuthMapper.updateByPrimaryKeySelective(auth);
                log.info("更新第三方认证信息成功 - 用户ID: {}, 平台: {}", userId, provider);
            }

        } catch (Exception e) {
            log.error("保存第三方认证信息失败 - 用户ID: {}, 登录方式: {}", userId, dto.getLoginWay(), e);
            // 不抛出异常，避免影响登录流程
        }
    }

    /**
     * 根据登录方式获取平台标识
     */
    private String getProviderFromLoginWay(String loginWay) {
        switch (loginWay) {
            case LoginWayConstant.LOGIN_BY_WEIXIN:
                return "wechat";
            case LoginWayConstant.LOGIN_BY_QQ:
                return "qq";
            case LoginWayConstant.LOGIN_BY_SINA:
                return "weibo";
            case LoginWayConstant.LOGIN_BY_APPLE_ID:
                return "apple";
            default:
                return null;
        }
    }

    private User findUserByThirdPartyInfo(ThirdPartyLoginDto dto) {
        try {
            UserThirdPartyAuth thirdPartyAuth = null;
            String provider = getProviderFromLoginWay(dto.getLoginWay());

            if (provider == null) {
                log.warn("不支持的登录方式: {}", dto.getLoginWay());
                return null;
            }

            // 优先通过unionId查找（跨应用统一身份）
            if (StrUtil.isNotEmpty(dto.getUnionId())) {
                thirdPartyAuth = userThirdPartyAuthMapper.selectByProviderAndUnionId(provider, dto.getUnionId());
            }

            // 如果通过unionId找不到，再通过openId查找
            if (thirdPartyAuth == null && StrUtil.isNotEmpty(dto.getOpenId())) {
                thirdPartyAuth = userThirdPartyAuthMapper.selectByProviderAndOpenId(provider, dto.getOpenId());
            }

            // Apple ID特殊处理
            if (thirdPartyAuth == null && "apple".equals(provider) && StrUtil.isNotEmpty(dto.getAppleAccount())) {
                thirdPartyAuth = userThirdPartyAuthMapper.selectByProviderAndOpenId(provider, dto.getAppleAccount());
            }

            // 如果找到第三方认证信息，则查找对应的用户
            if (thirdPartyAuth != null && thirdPartyAuth.getUserId() != null) {
                User user = userMapper.selectByPrimaryKey(thirdPartyAuth.getUserId());
                if (user != null) {
                    log.info("通过第三方信息找到现有用户 - 用户ID: {}, 平台: {}, openId: {}",
                            user.getId(), provider, maskAccount(dto.getOpenId()));

                    // 更新最后登录时间
                    userThirdPartyAuthMapper.updateLastLoginTime(thirdPartyAuth.getId(), new Date());

                    return user;
                }
            }

            return null;

        } catch (Exception e) {
            log.error("查找第三方用户信息异常 - 登录方式: {}", dto.getLoginWay(), e);
            return null;
        }
    }

    private String generateThirdPartyUsername(ThirdPartyLoginDto dto) {
        String prefix = "";
        String identifier = "";

        if (LoginWayConstant.LOGIN_BY_WEIXIN.equals(dto.getLoginWay())) {
            prefix = "wx_";
            // 优先使用unionId，其次使用openId
            identifier = StrUtil.isNotEmpty(dto.getUnionId()) ? dto.getUnionId() : dto.getOpenId();
        } else if (LoginWayConstant.LOGIN_BY_QQ.equals(dto.getLoginWay())) {
            prefix = "qq_";
            identifier = StrUtil.isNotEmpty(dto.getUnionId()) ? dto.getUnionId() : dto.getOpenId();
        } else if (LoginWayConstant.LOGIN_BY_SINA.equals(dto.getLoginWay())) {
            prefix = "wb_";
            identifier = dto.getOpenId(); // 微博使用openId
        } else if (LoginWayConstant.LOGIN_BY_APPLE_ID.equals(dto.getLoginWay())) {
            prefix = "apple_";
            identifier = dto.getAppleAccount();
        }

        // 如果identifier为空，使用时间戳
        if (StrUtil.isEmpty(identifier)) {
            identifier = String.valueOf(System.currentTimeMillis());
        }

        return prefix + identifier;
    }

    private TokenDto createTokenDto(User user) {
        // 生成包含用户ID的Token
        String token = jwtTokenProvider.generateToken(user.getUsername());
        String refreshToken = jwtTokenProvider.generateRefreshToken(user.getUsername());
        Date expiration = jwtTokenProvider.extractExpiration(token);
        Date refreshExpiration = jwtTokenProvider.extractRefreshExpiration(refreshToken);

        TokenDto tokenDto = new TokenDto();
        tokenDto.setUserId(user.getId());
        tokenDto.setUsername(user.getUsername());
        tokenDto.setAccessToken(token);
        tokenDto.setExpiration(expiration.getTime());
        tokenDto.setRefreshToken(refreshToken);
        tokenDto.setRefreshTokenExpiration(refreshExpiration.getTime());
        tokenDto.setParentMode(user.getParentMode());
        tokenDto.setRoleType(user.getRoleType());
        tokenDto.setGradeType(user.getGradeType());
        tokenDto.setNickname(user.getNickname());
        tokenDto.setAvatar(user.getAvatar());

        // 设置是否需要绑定手机号（默认不需要）
        tokenDto.setRequirePhoneBinding(false);

        // 设置登录方式（默认为空，在具体登录方法中设置）
        tokenDto.setLoginWay("");

        return tokenDto;
    }

    @Override
    public boolean resetPasswordByEmail(EmailResetPasswordDto dto) {
        try {
            // 1. 验证密码一致性
            if (!dto.getNewPassword().equals(dto.getConfirmPassword())) {
                throw new IllegalArgumentException("两次输入的密码不一致");
            }

            // 2. 验证密码强度
            PasswordStrengthValidator.PasswordValidationResult validationResult =
                    passwordStrengthValidator.validatePassword(dto.getNewPassword());
            if (!validationResult.isValid()) {
                throw new IllegalArgumentException(validationResult.getMessage());
            }

            // 3. 验证邮箱验证码
            if (!captchaService.verifyEmailCode(dto.getEmail(), dto.getCaptcha(), "reset_password")) {
                throw new IllegalArgumentException("验证码错误或已过期");
            }

            // 4. 查找用户
            User user = findUserByEmail(dto.getEmail());
            if (user == null) {
                throw new IllegalArgumentException("邮箱未注册");
            }

            // 5. 更新密码
            user.setPassword(passwordEncoder.encode(dto.getNewPassword()));
            user.setUpdateTime(new Date());

            int result = userMapper.updateByPrimaryKeySelective(user);
            if (result > 0) {
                log.info("邮箱重置密码成功 - 邮箱: {}, 用户ID: {}",
                        maskAccount(dto.getEmail()), user.getId());
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("邮箱重置密码失败 - 邮箱: {}", maskAccount(dto.getEmail()), e);
            throw e;
        }
    }

    @Override
    public boolean resetPasswordByPhone(PhoneResetPasswordDto dto) {
        try {
            // 1. 验证密码一致性
            if (!dto.getNewPassword().equals(dto.getConfirmPassword())) {
                throw new IllegalArgumentException("两次输入的密码不一致");
            }

            // 2. 验证密码强度
            PasswordStrengthValidator.PasswordValidationResult validationResult2 =
                    passwordStrengthValidator.validatePassword(dto.getNewPassword());
            if (!validationResult2.isValid()) {
                throw new IllegalArgumentException(validationResult2.getMessage());
            }

            // 3. 验证短信验证码
            if (!captchaService.verifyCode(dto.getPhone(), dto.getCaptcha())) {
                throw new IllegalArgumentException("验证码错误或已过期");
            }

            // 4. 查找用户
            User user = findUserByPhone(dto.getPhone());
            if (user == null) {
                throw new IllegalArgumentException("手机号未注册");
            }

            // 5. 更新密码
            user.setPassword(passwordEncoder.encode(dto.getNewPassword()));
            user.setUpdateTime(new Date());

            int result = userMapper.updateByPrimaryKeySelective(user);
            if (result > 0) {
                log.info("手机号重置密码成功 - 手机号: {}, 用户ID: {}",
                        maskAccount(dto.getPhone()), user.getId());
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("手机号重置密码失败 - 手机号: {}", maskAccount(dto.getPhone()), e);
            throw e;
        }
    }

    /**
     * 账号脱敏处理
     */
    private String maskAccount(String account) {
        if (account == null || account.length() <= 3) {
            return account;
        }

        if (account.contains("@")) {
            // 邮箱脱敏
            int atIndex = account.indexOf('@');
            if (atIndex > 1) {
                return account.charAt(0) + "***" + account.substring(atIndex);
            }
        } else if (account.matches("^1[3-9]\\d{9}$")) {
            // 手机号脱敏
            return account.substring(0, 3) + "****" + account.substring(7);
        }

        // 其他类型脱敏（如第三方openId）
        return account.substring(0, 2) + "***" + account.substring(account.length() - 1);
    }

    // ========== 临时添加的方法，用于修复编译错误 ==========

    /**
     * 生成用户Token
     */
    private TokenDto generateTokenForUser(User user, String loginWay) {
        // 使用正确的Token生成逻辑
        TokenDto tokenDto = createTokenDto(user);
        tokenDto.setLoginWay(loginWay);
        return tokenDto;
    }

    /**
     * 记录登录成功（临时实现）
     */
    private void recordLoginSuccess(String account, String loginWay) {
        // TODO: 实现真正的登录记录逻辑
        log.info("记录登录成功: account={}, loginWay={}", account, loginWay);
    }

    /**
     * 更新用户最后登录时间（临时实现）
     */
    private void updateUserLastLoginTime(Long userId) {
        // TODO: 实现真正的更新逻辑
        try {
            User updateUser = new User();
            updateUser.setId(userId);
            updateUser.setUpdateTime(new Date());
            userMapper.updateByPrimaryKeySelective(updateUser);
            log.info("更新用户最后登录时间: userId={}", userId);
        } catch (Exception e) {
            log.error("更新用户最后登录时间失败: userId={}", userId, e);
        }
    }

}
