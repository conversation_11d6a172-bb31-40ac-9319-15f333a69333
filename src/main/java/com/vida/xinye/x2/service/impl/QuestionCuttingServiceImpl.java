package com.vida.xinye.x2.service.impl;

import cn.hutool.core.util.IdUtil;
import com.vida.xinye.x2.adapter.QuestionCuttingAdapter;
import com.vida.xinye.x2.constant.QuestionCuttingConstant;
import com.vida.xinye.x2.dto.BatchQuestionCuttingResultDto;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import com.vida.xinye.x2.dto.param.QuestionCuttingRequest;
import com.vida.xinye.x2.service.QuestionCuttingProcessor;
import com.vida.xinye.x2.service.QuestionCuttingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 智能切题服务实现（简化版）
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
@Slf4j
@Service
public class QuestionCuttingServiceImpl implements QuestionCuttingService {

    @Autowired
    private QuestionCuttingProcessor processor;

    @Autowired
    private List<QuestionCuttingAdapter> adapters;

    private final ExecutorService batchExecutor = Executors.newFixedThreadPool(5);

    @Override
    public QuestionCuttingResultDto cutQuestion(QuestionCuttingRequest request) {
        log.info("开始处理切题请求 - 输入类型: {}, 策略: {}, 提供商: {}", 
                request.getInputType(), request.getStrategy(), request.getProvider());

        try {
            // 参数验证
            request.validate();

            // 委托给处理器
            return processor.process(request);

        } catch (Exception e) {
            log.error("切题处理失败: {}", e.getMessage(), e);
            
            QuestionCuttingResultDto result = new QuestionCuttingResultDto();
            result.setRequestId(IdUtil.simpleUUID());
            result.setSuccess(false);
            result.setErrorCode(QuestionCuttingConstant.ErrorCode.INTERNAL_ERROR);
            result.setErrorMessage("切题处理失败: " + e.getMessage());
            result.setCreateTime(new Date());
            return result;
        }
    }

    @Override
    public BatchQuestionCuttingResultDto batchCutQuestion(
            List<MultipartFile> images, 
            QuestionCuttingRequest request, 
            Boolean parallel) {

        String batchId = IdUtil.simpleUUID();
        log.info("开始批量切题处理 - 批次ID: {}, 图片数量: {}, 并行: {}", 
                batchId, images.size(), parallel);

        long startTime = System.currentTimeMillis();
        BatchQuestionCuttingResultDto batchResult = new BatchQuestionCuttingResultDto();
        batchResult.setBatchId(batchId);
        batchResult.setRequestId(IdUtil.simpleUUID());
        batchResult.setTotalCount(images.size());
        batchResult.setCreateTime(new Date());

        try {
            List<BatchQuestionCuttingResultDto.ImageResult> imageResults;

            if (Boolean.TRUE.equals(parallel)) {
                imageResults = processBatchParallel(images, request);
            } else {
                imageResults = processBatchSequential(images, request);
            }

            batchResult.setImageResults(imageResults);
            
            // 计算统计信息
            calculateBatchStats(batchResult);

            long totalTime = System.currentTimeMillis() - startTime;
            batchResult.setTotalProcessingTime(totalTime);
            batchResult.setCompleteTime(new Date());
            batchResult.setSuccess(batchResult.getFailureCount() == 0);

            log.info("批量切题处理完成 - 批次ID: {}, 成功: {}/{}, 总时间: {}ms", 
                    batchId, batchResult.getSuccessCount(), batchResult.getTotalCount(), totalTime);

            return batchResult;

        } catch (Exception e) {
            log.error("批量切题处理失败 - 批次ID: {}, 错误: {}", batchId, e.getMessage(), e);
            
            batchResult.setSuccess(false);
            batchResult.setTotalProcessingTime(System.currentTimeMillis() - startTime);
            batchResult.setCompleteTime(new Date());
            return batchResult;
        }
    }

    /**
     * 并行处理批量图片
     */
    private List<BatchQuestionCuttingResultDto.ImageResult> processBatchParallel(
            List<MultipartFile> images, QuestionCuttingRequest template) {

        List<CompletableFuture<BatchQuestionCuttingResultDto.ImageResult>> futures = 
            IntStream.range(0, images.size())
                .mapToObj(i -> CompletableFuture.supplyAsync(() -> 
                    processSingleImage(images.get(i), template, i), batchExecutor))
                .collect(Collectors.toList());

        return futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
    }

    /**
     * 串行处理批量图片
     */
    private List<BatchQuestionCuttingResultDto.ImageResult> processBatchSequential(
            List<MultipartFile> images, QuestionCuttingRequest template) {

        return IntStream.range(0, images.size())
                .mapToObj(i -> processSingleImage(images.get(i), template, i))
                .collect(Collectors.toList());
    }

    /**
     * 处理单张图片
     */
    private BatchQuestionCuttingResultDto.ImageResult processSingleImage(
            MultipartFile image, QuestionCuttingRequest template, int index) {

        Date startTime = new Date();
        BatchQuestionCuttingResultDto.ImageResult imageResult = new BatchQuestionCuttingResultDto.ImageResult();
        imageResult.setIndex(index);
        imageResult.setImageId(image.getOriginalFilename());
        imageResult.setStartTime(startTime);

        try {
            // 创建单独的请求
            QuestionCuttingRequest request = QuestionCuttingRequest.fromFile(image)
                    .strategy(template.getStrategy())
                    .provider(template.getProvider())
                    .subject(template.getSubject())
                    .grade(template.getGrade())
                    .needDetail(template.getNeedDetail())
                    .enableCache(template.getEnableCache())
                    .timeout(template.getTimeoutSeconds());

            // 处理切题
            QuestionCuttingResultDto result = cutQuestion(request);
            
            imageResult.setResult(result);
            imageResult.setSuccess(result.getSuccess());
            
            if (!result.getSuccess()) {
                imageResult.setErrorCode(result.getErrorCode());
                imageResult.setErrorMessage(result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("处理图片失败 - 索引: {}, 文件名: {}, 错误: {}", 
                     index, image.getOriginalFilename(), e.getMessage(), e);
            
            imageResult.setSuccess(false);
            imageResult.setErrorCode(QuestionCuttingConstant.ErrorCode.INTERNAL_ERROR);
            imageResult.setErrorMessage("处理失败: " + e.getMessage());
        }

        Date endTime = new Date();
        imageResult.setEndTime(endTime);
        imageResult.setProcessingTime(endTime.getTime() - startTime.getTime());

        return imageResult;
    }

    /**
     * 计算批量处理统计信息
     */
    private void calculateBatchStats(BatchQuestionCuttingResultDto batchResult) {
        List<BatchQuestionCuttingResultDto.ImageResult> results = batchResult.getImageResults();
        
        if (results == null || results.isEmpty()) {
            batchResult.setSuccessCount(0);
            batchResult.setFailureCount(0);
            return;
        }

        int successCount = (int) results.stream().filter(r -> Boolean.TRUE.equals(r.getSuccess())).count();
        int failureCount = results.size() - successCount;

        batchResult.setSuccessCount(successCount);
        batchResult.setFailureCount(failureCount);

        // 创建统计信息
        BatchQuestionCuttingResultDto.BatchStats stats = new BatchQuestionCuttingResultDto.BatchStats();
        
        // 计算平均处理时间
        OptionalDouble avgTime = results.stream()
                .filter(r -> r.getProcessingTime() != null)
                .mapToLong(BatchQuestionCuttingResultDto.ImageResult::getProcessingTime)
                .average();
        stats.setAverageProcessingTime(avgTime.orElse(0.0));

        // 计算成功率
        stats.setSuccessRate((double) successCount / results.size());

        // 计算总题目数量
        int totalQuestions = results.stream()
                .filter(r -> r.getResult() != null && r.getResult().getQuestions() != null)
                .mapToInt(r -> r.getResult().getQuestions().size())
                .sum();
        stats.setTotalQuestionCount(totalQuestions);

        batchResult.setBatchStats(stats);
    }

    @Override
    public List<String> getSupportedProviders() {
        return processor.getSupportedProviders();
    }

    @Override
    public List<String> getSupportedStrategies() {
        return Arrays.asList(
                QuestionCuttingConstant.Strategy.BEST_QUALITY,
                QuestionCuttingConstant.Strategy.FASTEST,
                QuestionCuttingConstant.Strategy.AGGREGATED,
                QuestionCuttingConstant.Strategy.SPECIFIED,
                QuestionCuttingConstant.Strategy.ROUND_ROBIN
        );
    }

    @Override
    public Map<String, Object> getSystemStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 适配器状态
        Map<String, Boolean> adapterStatus = adapters.stream()
                .collect(Collectors.toMap(
                        adapter -> adapter.getProvider().getCode(),
                        QuestionCuttingAdapter::isAvailable
                ));
        stats.put("adapters", adapterStatus);
        
        // 支持的策略
        stats.put("strategies", getSupportedStrategies());
        
        // 系统信息
        stats.put("timestamp", new Date());
        stats.put("version", "2.0-simplified");
        
        return stats;
    }
}
