package com.vida.xinye.x2.adapter.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.aliyun.credentials.Client;
import com.aliyun.ocr_api20210707.models.RecognizeEduPaperStructedRequest;
import com.aliyun.ocr_api20210707.models.RecognizeEduPaperStructedResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.vida.xinye.x2.adapter.QuestionCuttingAdapter;
import com.vida.xinye.x2.config.QuestionCuttingConfig;
import com.vida.xinye.x2.constant.QuestionCuttingConstant;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyRequestDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyResponseDto;
import com.vida.xinye.x2.enums.QuestionCuttingEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.Base64;

/**
 * 阿里云OCR切题适配器
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Slf4j
@Component
public class AliyunAdapter implements QuestionCuttingAdapter {

    @Autowired
    private QuestionCuttingConfig config;

    @Override
    public QuestionCuttingEnum.Provider getProvider() {
        return QuestionCuttingEnum.Provider.ALIYUN;
    }

    @Override
    public boolean isAvailable() {
        QuestionCuttingConfig.AliyunConfig aliyunConfig = config.getAliyun();
        return aliyunConfig != null &&
                aliyunConfig.isEnabled() &&
                StrUtil.isNotBlank(aliyunConfig.getAccessKeyId()) &&
                StrUtil.isNotBlank(aliyunConfig.getAccessKeySecret()) &&
                StrUtil.isNotBlank(aliyunConfig.getEndpoint());
    }

    @Override
    public int getPriority() {
        return 30; // 较低优先级
    }

    @Override
    public int getQualityScore() {
        return 4; // 高质量评分
    }

    @Override
    public long getAverageResponseTime() {
        return 5000; // 5秒平均响应时间
    }

    @Override
    public ThirdPartyResponseDto callThirdPartyApi(ThirdPartyRequestDto request) {
        long startTime = System.currentTimeMillis();
        ThirdPartyResponseDto.AliyunResponse response = new ThirdPartyResponseDto.AliyunResponse();
        response.setRequestId(request.getRequestId());

        try {
            // 创建阿里云客户端
            com.aliyun.ocr_api20210707.Client client = createAliyunClient();

            // 构建请求参数
            RecognizeEduPaperStructedRequest apiRequest = buildApiRequest(request);

            // 构建运行时选项
            RuntimeOptions runtime = new RuntimeOptions();
            runtime.setConnectTimeout(config.getAliyun().getTimeout());
            runtime.setReadTimeout(config.getAliyun().getTimeout());

            log.info("调用阿里云接口 - 请求ID: {}, Endpoint: {}", request.getRequestId(), config.getAliyun().getEndpoint());

            // 调用API
            RecognizeEduPaperStructedResponse apiResponse = client.recognizeEduPaperStructedWithOptions(apiRequest, runtime);
            log.info("调用阿里云接口 - 响应值：{}", JSONUtil.toJsonStr(apiResponse));
            long responseTime = System.currentTimeMillis() - startTime;
            response.setResponseTime(responseTime);

            log.info("阿里云接口响应 - 请求ID: {}, 响应时间: {}ms", request.getRequestId(), responseTime);

            // 解析响应
            parseAliyunApiResponse(apiResponse, response);

            response.setSuccess(true);

        } catch (TeaException e) {
            long responseTime = System.currentTimeMillis() - startTime;
            response.setResponseTime(responseTime);
            response.setSuccess(false);
            response.setErrorCode(QuestionCuttingConstant.ErrorCode.API_CALL_FAILED);
            response.setErrorMessage("阿里云接口调用失败: " + e.getMessage());

            log.error("阿里云接口调用失败 - 请求ID: {}, 错误码: {}, 错误信息: {}",
                    request.getRequestId(), e.getCode(), e.getMessage(), e);
        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            response.setResponseTime(responseTime);
            response.setSuccess(false);
            response.setErrorCode(QuestionCuttingConstant.ErrorCode.API_CALL_FAILED);
            response.setErrorMessage("阿里云接口调用失败: " + e.getMessage());

            log.error("阿里云接口调用失败 - 请求ID: {}, 错误: {}", request.getRequestId(), e.getMessage(), e);
        }

        return response;
    }

    @Override
    public QuestionCuttingResultDto convertToStandardResult(ThirdPartyResponseDto thirdPartyResponse, String requestId) {
        QuestionCuttingResultDto result = new QuestionCuttingResultDto();
        result.setRequestId(requestId);
        result.setProvider(getProvider().getCode());
        result.setCreateTime(new Date());

        if (!thirdPartyResponse.getSuccess()) {
            result.setSuccess(false);
            result.setErrorCode(thirdPartyResponse.getErrorCode());
            result.setErrorMessage(thirdPartyResponse.getErrorMessage());
            return result;
        }

        try {
            ThirdPartyResponseDto.AliyunResponse aliyunResponse =
                    (ThirdPartyResponseDto.AliyunResponse) thirdPartyResponse;

            result.setSuccess(true);
            result.setProcessingTime(aliyunResponse.getResponseTime());

            if (aliyunResponse.getData() != null) {
                // 优先使用新的part_info结构，如果没有则使用旧的structuredResult
                List<QuestionCuttingResultDto.QuestionInfo> questions;
                if (aliyunResponse.getData().getPartInfo() != null && !aliyunResponse.getData().getPartInfo().isEmpty()) {
                    questions = convertPartInfoToQuestions(aliyunResponse.getData().getPartInfo());
                } else {
                    questions = convertAliyunResults(aliyunResponse.getData().getStructuredResult());
                }
                result.setQuestions(questions);

                // 计算整体置信度和质量评分
                double avgConfidence = questions.stream()
                        .filter(q -> q.getConfidence() != null)
                        .mapToDouble(QuestionCuttingResultDto.QuestionInfo::getConfidence)
                        .average()
                        .orElse(0.85);

                result.setConfidence(avgConfidence);
                result.setQualityScore(calculateQualityScore(avgConfidence));

                // 设置原始图片信息
                QuestionCuttingResultDto.ImageInfo imageInfo = new QuestionCuttingResultDto.ImageInfo();
                imageInfo.setWidth(aliyunResponse.getData().getOrgWidth() != null ?
                        aliyunResponse.getData().getOrgWidth() : aliyunResponse.getData().getWidth());
                imageInfo.setHeight(aliyunResponse.getData().getOrgHeight() != null ?
                        aliyunResponse.getData().getOrgHeight() : aliyunResponse.getData().getHeight());
                result.setOriginalImage(imageInfo);
            }

        } catch (Exception e) {
            log.error("转换阿里云响应失败 - 请求ID: {}, 错误: {}", requestId, e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorCode(QuestionCuttingConstant.ErrorCode.INVALID_API_RESPONSE);
            result.setErrorMessage("响应格式转换失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 创建阿里云客户端
     */
    private com.aliyun.ocr_api20210707.Client createAliyunClient() throws Exception {
        // 使用AccessKey和AccessKeySecret创建凭据
        com.aliyun.credentials.models.Config credentialConfig = new com.aliyun.credentials.models.Config();
        credentialConfig.type = "access_key";
        credentialConfig.accessKeyId = this.config.getAliyun().getAccessKeyId();
        credentialConfig.accessKeySecret = this.config.getAliyun().getAccessKeySecret();

        Client credential = new Client(credentialConfig);

        Config config = new Config();
        config.setCredential(credential);

        // 设置Endpoint
        config.endpoint = this.config.getAliyun().getEndpoint();

        return new com.aliyun.ocr_api20210707.Client(config);
    }

    /**
     * 构建API请求参数
     */
    private RecognizeEduPaperStructedRequest buildApiRequest(ThirdPartyRequestDto request) {
        RecognizeEduPaperStructedRequest apiRequest = new RecognizeEduPaperStructedRequest();

        // 设置图片数据
        if (StrUtil.isNotBlank(request.getImageUrl())) {
            apiRequest.setUrl(request.getImageUrl());
        } else if (StrUtil.isNotBlank(request.getImageBase64())) {
            // 对于Base64数据，需要转换为InputStream
            byte[] imageBytes = Base64.getDecoder().decode(request.getImageBase64());
            java.io.ByteArrayInputStream inputStream = new java.io.ByteArrayInputStream(imageBytes);
            apiRequest.setBody(inputStream);
        }

        // 设置可选参数
        if (StrUtil.isNotBlank(request.getSubject())) {
            apiRequest.setSubject(mapSubjectToAliyun(request.getSubject()));
        }

        // 设置其他参数
        apiRequest.setNeedRotate(config.getAliyun().getNeedRotate());
        apiRequest.setOutputOricoord(config.getAliyun().getOutputOricoord());

        return apiRequest;
    }

    /**
     * 映射学科类型到阿里云格式
     */
    private String mapSubjectToAliyun(String subject) {
        if (StrUtil.isBlank(subject)) {
            return "default";
        }

        switch (subject.toLowerCase()) {
            case "math":
                return "Math";
            case "chinese":
                return "Chinese";
            case "english":
                return "English";
            case "physics":
                return "Physics";
            case "chemistry":
                return "Chemistry";
            case "biology":
                return "Biology";
            case "history":
                return "History";
            case "geography":
                return "Geography";
            case "politics":
                return "Politics";
            default:
                return "default";
        }
    }

    /**
     * 解析阿里云API响应
     */
    private void parseAliyunApiResponse(RecognizeEduPaperStructedResponse apiResponse,
                                        ThirdPartyResponseDto.AliyunResponse response) {
        try {
            if (apiResponse == null || apiResponse.getBody() == null) {
                log.warn("阿里云API响应为空");
                return;
            }

            response.setRequestId(apiResponse.getBody().getRequestId());

            // 检查是否有错误
            if (StrUtil.isNotBlank(apiResponse.getBody().getCode())) {
                log.error("阿里云API返回错误 - 错误码: {}, 错误信息: {}",
                        apiResponse.getBody().getCode(), apiResponse.getBody().getMessage());
                throw new RuntimeException("阿里云API错误: " + apiResponse.getBody().getMessage());
            }

            if (apiResponse.getBody().getData() != null) {
                // 解析Data字段（JSON字符串）
                String dataJson = apiResponse.getBody().getData();
                if (StrUtil.isBlank(dataJson)) {
                    log.warn("阿里云API响应数据为空");
                    return;
                }

                Map<String, Object> dataMap = JSONUtil.toBean(dataJson, Map.class);

                ThirdPartyResponseDto.AliyunResponse.AliyunData data =
                        new ThirdPartyResponseDto.AliyunResponse.AliyunData();

                // 解析基本信息（添加空值检查）
                data.setHeight(getIntegerValue(dataMap, "height"));
                data.setWidth(getIntegerValue(dataMap, "width"));
                data.setOrgHeight(getIntegerValue(dataMap, "orgHeight"));
                data.setOrgWidth(getIntegerValue(dataMap, "orgWidth"));
                data.setPageId(getIntegerValue(dataMap, "page_id"));
                data.setPageTitle((String) dataMap.get("page_title"));
                data.setPrismVersion((String) dataMap.get("prism_version"));
                data.setPrismWnum(getIntegerValue(dataMap, "prism_wnum"));

                // 解析part_info
                List<Map<String, Object>> partInfoMaps =
                        (List<Map<String, Object>>) dataMap.get("part_info");
                if (partInfoMaps != null) {
                    List<ThirdPartyResponseDto.AliyunResponse.AliyunPartInfo> partInfoList =
                            partInfoMaps.stream()
                                    .map(this::parsePartInfo)
                                    .collect(Collectors.toList());
                    data.setPartInfo(partInfoList);
                }

                // 解析figure
                List<Map<String, Object>> figureMaps =
                        (List<Map<String, Object>>) dataMap.get("figure");
                if (figureMaps != null) {
                    List<ThirdPartyResponseDto.AliyunResponse.AliyunFigure> figureList =
                            figureMaps.stream()
                                    .map(this::parseFigure)
                                    .collect(Collectors.toList());
                    data.setFigure(figureList);
                }

                response.setData(data);
            }

        } catch (Exception e) {
            log.error("解析阿里云API响应失败: {}", e.getMessage(), e);
            throw new RuntimeException("解析阿里云API响应失败", e);
        }
    }

    /**
     * 构建请求参数（根据官方文档）
     */
    private Map<String, Object> buildRequestParams(ThirdPartyRequestDto.AliyunRequest request) {
        Map<String, Object> params = new HashMap<>();

        // 根据官方文档，参数名应该是大写
        if (StrUtil.isNotBlank(request.getImageBase64())) {
            params.put("body", request.getImageBase64());  // 图片二进制文件
        } else if (StrUtil.isNotBlank(request.getImageUrl())) {
            params.put("Url", request.getImageUrl());      // 注意大写
        }

        // 可选参数（根据官方文档）
        if (StrUtil.isNotBlank(request.getSubject())) {
            params.put("Subject", request.getSubject());   // 学科
        }

        params.put("NeedRotate", request.getNeedRotate());           // 是否需要旋转
        params.put("OutputOricoord", request.getOutputOricoord());   // 是否输出原始坐标

        log.debug("阿里云请求参数构建完成 - 参数数量: {}", params.size());

        return params;
    }

    /**
     * 构建请求头
     */
    private Map<String, String> buildHeaders(ThirdPartyRequestDto.AliyunRequest request) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");

        // 阿里云签名认证（简化版本，实际应该使用阿里云SDK）
        headers.put("Authorization", "Bearer " + generateAccessToken(request));

        return headers;
    }

    /**
     * 生成访问令牌（简化版本）
     */
    private String generateAccessToken(ThirdPartyRequestDto.AliyunRequest request) {
        // 实际应该使用阿里云SDK生成签名
        // 这里简化处理，返回AccessKeyId
        return request.getAccessKeyId();
    }

    /**
     * 解析阿里云响应
     */
    private void parseAliyunResponse(String responseBody, ThirdPartyResponseDto.AliyunResponse response) {
        try {
            Map<String, Object> responseMap = JSONUtil.toBean(responseBody, Map.class);
            response.setRawData(responseMap);

            String requestId = (String) responseMap.get("RequestId");
            response.setRequestId(requestId);

            // 解析数据部分
            Map<String, Object> dataMap = (Map<String, Object>) responseMap.get("Data");
            if (dataMap != null) {
                ThirdPartyResponseDto.AliyunResponse.AliyunData data =
                        new ThirdPartyResponseDto.AliyunResponse.AliyunData();

                data.setAngle((Integer) dataMap.get("angle"));
                data.setHeight((Integer) dataMap.get("height"));
                data.setWidth((Integer) dataMap.get("width"));

                // 解析结构化结果
                List<Map<String, Object>> structuredMaps =
                        (List<Map<String, Object>>) dataMap.get("structuredResult");
                if (structuredMaps != null) {
                    List<ThirdPartyResponseDto.AliyunResponse.AliyunStructuredResult> structuredResults =
                            structuredMaps.stream()
                                    .map(this::parseStructuredResult)
                                    .collect(Collectors.toList());
                    data.setStructuredResult(structuredResults);
                }

                response.setData(data);
            }

        } catch (Exception e) {
            log.error("解析阿里云响应失败: {}", e.getMessage(), e);
            throw new RuntimeException("解析阿里云响应失败", e);
        }
    }

    /**
     * 解析结构化结果
     */
    private ThirdPartyResponseDto.AliyunResponse.AliyunStructuredResult parseStructuredResult(
            Map<String, Object> resultMap) {

        ThirdPartyResponseDto.AliyunResponse.AliyunStructuredResult result =
                new ThirdPartyResponseDto.AliyunResponse.AliyunStructuredResult();

        result.setType((String) resultMap.get("type"));
        result.setValue((String) resultMap.get("value"));
        result.setConfidence((Double) resultMap.get("confidence"));

        // 解析位置信息
        Map<String, Object> posMap = (Map<String, Object>) resultMap.get("pos");
        if (posMap != null) {
            ThirdPartyResponseDto.AliyunResponse.AliyunPosition position = parsePosition(posMap);
            result.setPos(position);
        }

        // 解析子结果
        List<Map<String, Object>> subResultMaps = (List<Map<String, Object>>) resultMap.get("subResults");
        if (subResultMaps != null) {
            List<ThirdPartyResponseDto.AliyunResponse.AliyunStructuredResult> subResults =
                    subResultMaps.stream()
                            .map(this::parseStructuredResult)
                            .collect(Collectors.toList());
            result.setSubResults(subResults);
        }

        return result;
    }

    /**
     * 解析位置信息
     */
    private ThirdPartyResponseDto.AliyunResponse.AliyunPosition parsePosition(Map<String, Object> posMap) {
        ThirdPartyResponseDto.AliyunResponse.AliyunPosition position =
                new ThirdPartyResponseDto.AliyunResponse.AliyunPosition();

        position.setX((Integer) posMap.get("x"));
        position.setY((Integer) posMap.get("y"));
        position.setW((Integer) posMap.get("w"));
        position.setH((Integer) posMap.get("h"));

        // 解析顶点坐标
        List<Map<String, Object>> pointMaps = (List<Map<String, Object>>) posMap.get("points");
        if (pointMaps != null) {
            List<ThirdPartyResponseDto.AliyunResponse.AliyunPoint> points =
                    pointMaps.stream()
                            .map(pointMap -> {
                                ThirdPartyResponseDto.AliyunResponse.AliyunPoint point =
                                        new ThirdPartyResponseDto.AliyunResponse.AliyunPoint();
                                point.setX((Integer) pointMap.get("x"));
                                point.setY((Integer) pointMap.get("y"));
                                return point;
                            })
                            .collect(Collectors.toList());
            position.setPoints(points);
        }

        return position;
    }

    /**
     * 转换part_info为标准题目格式
     */
    private List<QuestionCuttingResultDto.QuestionInfo> convertPartInfoToQuestions(
            List<ThirdPartyResponseDto.AliyunResponse.AliyunPartInfo> partInfoList) {

        List<QuestionCuttingResultDto.QuestionInfo> questions = new ArrayList<>();
        int questionNumber = 1;

        for (ThirdPartyResponseDto.AliyunResponse.AliyunPartInfo partInfo : partInfoList) {
            if (partInfo.getSubjectList() != null) {
                for (ThirdPartyResponseDto.AliyunResponse.AliyunSubject subject : partInfo.getSubjectList()) {
                    QuestionCuttingResultDto.QuestionInfo question = convertSubjectToQuestion(subject, questionNumber++, partInfo.getPartTitle());
                    if (question != null) {
                        questions.add(question);
                    }
                }
            }
        }

        return questions;
    }

    /**
     * 转换subject为标准题目格式
     */
    private QuestionCuttingResultDto.QuestionInfo convertSubjectToQuestion(
            ThirdPartyResponseDto.AliyunResponse.AliyunSubject subject, int questionNumber, String partTitle) {

        if (StrUtil.isBlank(subject.getText())) {
            return null;
        }

        QuestionCuttingResultDto.QuestionInfo question = new QuestionCuttingResultDto.QuestionInfo();
        question.setQuestionId("aliyun_" + questionNumber);
        question.setQuestionNumber(questionNumber);
        question.setContent(subject.getText());
        question.setConfidence(subject.getProb() != null ? subject.getProb() / 100.0 : 0.85);
        question.setQuestionType(mapAliyunTypeToStandard(subject.getType()));
        // 将题型标题存储在扩展属性中
        if (question.getExtraProperties() == null) {
            question.setExtraProperties(new java.util.HashMap<>());
        }
        question.getExtraProperties().put("category", partTitle);

        // 转换位置信息
        if (subject.getPosList() != null && !subject.getPosList().isEmpty()) {
            QuestionCuttingResultDto.BoundingBox boundingBox = convertAliyunPosList(subject.getPosList().get(0));
            question.setBoundingBox(boundingBox);
        }

        // 处理选项信息
        if (subject.getElementList() != null) {
            List<QuestionCuttingResultDto.OptionInfo> options = new ArrayList<>();
            for (ThirdPartyResponseDto.AliyunResponse.AliyunElement element : subject.getElementList()) {
                if (element.getType() != null && element.getType() == 1 && element.getContentList() != null) { // 选项类型
                    for (ThirdPartyResponseDto.AliyunResponse.AliyunContent content : element.getContentList()) {
                        if (StrUtil.isNotBlank(content.getString())) {
                            QuestionCuttingResultDto.OptionInfo option = new QuestionCuttingResultDto.OptionInfo();
                            option.setContent(content.getString());
                            String label = extractOptionLabel(content.getString());
                            option.setLabel(label);
                            options.add(option);
                        }
                    }
                }
            }
            if (!options.isEmpty()) {
                question.setOptions(options);
            }
        }

        return question;
    }

    /**
     * 映射阿里云题目类型到标准类型
     */
    private String mapAliyunTypeToStandard(Integer aliyunType) {
        if (aliyunType == null) {
            return QuestionCuttingConstant.QuestionType.OTHER;
        }

        switch (aliyunType) {
            case 0:
                return QuestionCuttingConstant.QuestionType.CHOICE;
            case 1:
                return QuestionCuttingConstant.QuestionType.FILL_BLANK;
            case 5:
                return QuestionCuttingConstant.QuestionType.ANSWER;
            case 8:
                return QuestionCuttingConstant.QuestionType.CALCULATION;
            case 9:
                return QuestionCuttingConstant.QuestionType.JUDGE;
            case 15:
                return QuestionCuttingConstant.QuestionType.ANSWER;
            default:
                return QuestionCuttingConstant.QuestionType.OTHER;
        }
    }

    /**
     * 转换阿里云位置列表为边界框
     */
    private QuestionCuttingResultDto.BoundingBox convertAliyunPosList(
            List<ThirdPartyResponseDto.AliyunResponse.AliyunPoint> pointList) {

        QuestionCuttingResultDto.BoundingBox boundingBox = new QuestionCuttingResultDto.BoundingBox();

        if (pointList != null && pointList.size() >= 4) {
            // 计算宽度和高度
            int minX = pointList.stream().mapToInt(ThirdPartyResponseDto.AliyunResponse.AliyunPoint::getX).min().orElse(0);
            int maxX = pointList.stream().mapToInt(ThirdPartyResponseDto.AliyunResponse.AliyunPoint::getX).max().orElse(0);
            int minY = pointList.stream().mapToInt(ThirdPartyResponseDto.AliyunResponse.AliyunPoint::getY).min().orElse(0);
            int maxY = pointList.stream().mapToInt(ThirdPartyResponseDto.AliyunResponse.AliyunPoint::getY).max().orElse(0);

            boundingBox.setWidth(maxX - minX);
            boundingBox.setHeight(maxY - minY);

            // 转换顶点坐标
            List<QuestionCuttingResultDto.Point> points = pointList.stream()
                    .map(aliyunPoint -> {
                        QuestionCuttingResultDto.Point point = new QuestionCuttingResultDto.Point();
                        point.setX(aliyunPoint.getX());
                        point.setY(aliyunPoint.getY());
                        return point;
                    })
                    .collect(Collectors.toList());
            boundingBox.setPoints(points);
        }

        return boundingBox;
    }

    /**
     * 转换阿里云结果为标准格式（兼容旧版本）
     */
    private List<QuestionCuttingResultDto.QuestionInfo> convertAliyunResults(
            List<ThirdPartyResponseDto.AliyunResponse.AliyunStructuredResult> aliyunResults) {

        if (aliyunResults == null) {
            return new ArrayList<>();
        }

        List<QuestionCuttingResultDto.QuestionInfo> questions = new ArrayList<>();
        int questionNumber = 1;

        for (ThirdPartyResponseDto.AliyunResponse.AliyunStructuredResult result : aliyunResults) {
            if ("question".equals(result.getType()) || "text".equals(result.getType())) {
                QuestionCuttingResultDto.QuestionInfo question = convertAliyunQuestion(result, questionNumber++);
                if (question != null) {
                    questions.add(question);
                }
            }
        }

        return questions;
    }

    /**
     * 转换单个阿里云题目
     */
    private QuestionCuttingResultDto.QuestionInfo convertAliyunQuestion(
            ThirdPartyResponseDto.AliyunResponse.AliyunStructuredResult result, int questionNumber) {

        if (StrUtil.isBlank(result.getValue())) {
            return null;
        }

        QuestionCuttingResultDto.QuestionInfo question = new QuestionCuttingResultDto.QuestionInfo();
        question.setQuestionId("aliyun_" + questionNumber);
        question.setQuestionNumber(questionNumber);
        question.setContent(result.getValue());
        question.setConfidence(result.getConfidence());
        question.setQuestionType(detectQuestionType(result.getValue()));

        // 转换位置信息
        if (result.getPos() != null) {
            QuestionCuttingResultDto.BoundingBox boundingBox = convertAliyunBoundingBox(result.getPos());
            question.setBoundingBox(boundingBox);
        }

        // 处理子结果（选项等）
        if (result.getSubResults() != null && !result.getSubResults().isEmpty()) {
            List<QuestionCuttingResultDto.OptionInfo> options = new ArrayList<>();
            for (ThirdPartyResponseDto.AliyunResponse.AliyunStructuredResult subResult : result.getSubResults()) {
                if ("option".equals(subResult.getType()) && StrUtil.isNotBlank(subResult.getValue())) {
                    QuestionCuttingResultDto.OptionInfo option = new QuestionCuttingResultDto.OptionInfo();
                    option.setContent(subResult.getValue());
                    // 从内容中提取选项标识
                    String label = extractOptionLabel(subResult.getValue());
                    option.setLabel(label);
                    options.add(option);
                }
            }
            if (!options.isEmpty()) {
                question.setOptions(options);
            }
        }

        return question;
    }

    /**
     * 转换阿里云边界框
     */
    private QuestionCuttingResultDto.BoundingBox convertAliyunBoundingBox(
            ThirdPartyResponseDto.AliyunResponse.AliyunPosition aliyunPos) {

        QuestionCuttingResultDto.BoundingBox boundingBox = new QuestionCuttingResultDto.BoundingBox();

        if (aliyunPos.getW() != null && aliyunPos.getH() != null) {
            boundingBox.setWidth(aliyunPos.getW());
            boundingBox.setHeight(aliyunPos.getH());
        }

        // 转换顶点坐标
        if (aliyunPos.getPoints() != null) {
            List<QuestionCuttingResultDto.Point> points = aliyunPos.getPoints().stream()
                    .map(aliyunPoint -> {
                        QuestionCuttingResultDto.Point point = new QuestionCuttingResultDto.Point();
                        point.setX(aliyunPoint.getX());
                        point.setY(aliyunPoint.getY());
                        return point;
                    })
                    .collect(Collectors.toList());
            boundingBox.setPoints(points);
        }

        return boundingBox;
    }

    /**
     * 检测题目类型
     */
    private String detectQuestionType(String content) {
        if (StrUtil.isBlank(content)) {
            return QuestionCuttingConstant.QuestionType.OTHER;
        }

        String lowerContent = content.toLowerCase();

        if (lowerContent.contains("a.") || lowerContent.contains("b.") ||
                lowerContent.contains("a、") || lowerContent.contains("b、") ||
                lowerContent.contains("选择")) {
            return QuestionCuttingConstant.QuestionType.CHOICE;
        } else if (lowerContent.contains("填空") || lowerContent.contains("____") ||
                lowerContent.contains("（）") || lowerContent.contains("()")) {
            return QuestionCuttingConstant.QuestionType.FILL_BLANK;
        } else if (lowerContent.contains("判断") || lowerContent.contains("对错") ||
                lowerContent.contains("正确") || lowerContent.contains("错误")) {
            return QuestionCuttingConstant.QuestionType.JUDGE;
        } else if (lowerContent.contains("计算") || lowerContent.contains("求") ||
                lowerContent.contains("=") || lowerContent.contains("解：")) {
            return QuestionCuttingConstant.QuestionType.CALCULATION;
        } else if (lowerContent.contains("解答") || lowerContent.contains("分析") ||
                lowerContent.contains("证明") || lowerContent.contains("说明")) {
            return QuestionCuttingConstant.QuestionType.ANSWER;
        }

        return QuestionCuttingConstant.QuestionType.OTHER;
    }

    /**
     * 提取选项标识
     */
    private String extractOptionLabel(String optionContent) {
        if (StrUtil.isBlank(optionContent)) {
            return "";
        }

        // 匹配 A. B. C. D. 格式
        if (optionContent.matches("^[A-Z]\\..*")) {
            return optionContent.substring(0, 1);
        }

        // 匹配 A、B、C、D、格式
        if (optionContent.matches("^[A-Z]、.*")) {
            return optionContent.substring(0, 1);
        }

        // 匹配 (A) (B) (C) (D) 格式
        if (optionContent.matches("^\\([A-Z]\\).*")) {
            return optionContent.substring(1, 2);
        }

        // 默认返回空字符串
        return "";
    }

    /**
     * 根据置信度计算质量评分
     */
    private Integer calculateQualityScore(Double confidence) {
        if (confidence == null) {
            return 3;
        }

        if (confidence >= 0.95) {
            return 5;
        } else if (confidence >= 0.85) {
            return 4;
        } else if (confidence >= 0.7) {
            return 3;
        } else if (confidence >= 0.5) {
            return 2;
        } else {
            return 1;
        }
    }

    /**
     * 解析part_info
     */
    private ThirdPartyResponseDto.AliyunResponse.AliyunPartInfo parsePartInfo(Map<String, Object> partInfoMap) {
        ThirdPartyResponseDto.AliyunResponse.AliyunPartInfo partInfo =
                new ThirdPartyResponseDto.AliyunResponse.AliyunPartInfo();

        partInfo.setPartTitle((String) partInfoMap.get("part_title"));

        // 解析pos_list
        List<List<Map<String, Object>>> posListMaps =
                (List<List<Map<String, Object>>>) partInfoMap.get("pos_list");
        if (posListMaps != null) {
            List<List<ThirdPartyResponseDto.AliyunResponse.AliyunPoint>> posList =
                    posListMaps.stream()
                            .map(pointMaps -> pointMaps.stream()
                                    .map(this::parsePoint)
                                    .collect(Collectors.toList()))
                            .collect(Collectors.toList());
            partInfo.setPosList(posList);
        }

        // 解析subject_list
        List<Map<String, Object>> subjectListMaps =
                (List<Map<String, Object>>) partInfoMap.get("subject_list");
        if (subjectListMaps != null) {
            List<ThirdPartyResponseDto.AliyunResponse.AliyunSubject> subjectList =
                    subjectListMaps.stream()
                            .map(this::parseSubject)
                            .collect(Collectors.toList());
            partInfo.setSubjectList(subjectList);
        }

        return partInfo;
    }

    /**
     * 解析subject
     */
    private ThirdPartyResponseDto.AliyunResponse.AliyunSubject parseSubject(Map<String, Object> subjectMap) {
        ThirdPartyResponseDto.AliyunResponse.AliyunSubject subject =
                new ThirdPartyResponseDto.AliyunResponse.AliyunSubject();

        subject.setIndex(getIntegerValue(subjectMap, "index"));
        subject.setType(getIntegerValue(subjectMap, "type"));
        subject.setText((String) subjectMap.get("text"));
        subject.setProb(getIntegerValue(subjectMap, "prob"));
        subject.setNumChoices(getIntegerValue(subjectMap, "num_choices"));

        // 解析pos_list
        List<List<Map<String, Object>>> posListMaps =
                (List<List<Map<String, Object>>>) subjectMap.get("pos_list");
        if (posListMaps != null) {
            List<List<ThirdPartyResponseDto.AliyunResponse.AliyunPoint>> posList =
                    posListMaps.stream()
                            .map(pointMaps -> pointMaps.stream()
                                    .map(this::parsePoint)
                                    .collect(Collectors.toList()))
                            .collect(Collectors.toList());
            subject.setPosList(posList);
        }

        // 解析element_list
        List<Map<String, Object>> elementListMaps =
                (List<Map<String, Object>>) subjectMap.get("element_list");
        if (elementListMaps != null) {
            List<ThirdPartyResponseDto.AliyunResponse.AliyunElement> elementList =
                    elementListMaps.stream()
                            .map(this::parseElement)
                            .collect(Collectors.toList());
            subject.setElementList(elementList);
        }

        return subject;
    }

    /**
     * 解析element
     */
    private ThirdPartyResponseDto.AliyunResponse.AliyunElement parseElement(Map<String, Object> elementMap) {
        ThirdPartyResponseDto.AliyunResponse.AliyunElement element =
                new ThirdPartyResponseDto.AliyunResponse.AliyunElement();

        element.setType((Integer) elementMap.get("type"));
        element.setText((String) elementMap.get("text"));

        // 解析pos_list
        List<List<Map<String, Object>>> posListMaps =
                (List<List<Map<String, Object>>>) elementMap.get("pos_list");
        if (posListMaps != null) {
            List<List<ThirdPartyResponseDto.AliyunResponse.AliyunPoint>> posList =
                    posListMaps.stream()
                            .map(pointMaps -> pointMaps.stream()
                                    .map(this::parsePoint)
                                    .collect(Collectors.toList()))
                            .collect(Collectors.toList());
            element.setPosList(posList);
        }

        // 解析content_list
        List<Map<String, Object>> contentListMaps =
                (List<Map<String, Object>>) elementMap.get("content_list");
        if (contentListMaps != null) {
            List<ThirdPartyResponseDto.AliyunResponse.AliyunContent> contentList =
                    contentListMaps.stream()
                            .map(this::parseContent)
                            .collect(Collectors.toList());
            element.setContentList(contentList);
        }

        return element;
    }

    /**
     * 解析content
     */
    private ThirdPartyResponseDto.AliyunResponse.AliyunContent parseContent(Map<String, Object> contentMap) {
        ThirdPartyResponseDto.AliyunResponse.AliyunContent content =
                new ThirdPartyResponseDto.AliyunResponse.AliyunContent();

        content.setType((Integer) contentMap.get("type"));
        content.setProb((Integer) contentMap.get("prob"));
        content.setString((String) contentMap.get("string"));
        content.setOption((String) contentMap.get("option"));

        // 解析pos
        List<Map<String, Object>> posMaps = (List<Map<String, Object>>) contentMap.get("pos");
        if (posMaps != null) {
            List<ThirdPartyResponseDto.AliyunResponse.AliyunPoint> pos =
                    posMaps.stream()
                            .map(this::parsePoint)
                            .collect(Collectors.toList());
            content.setPos(pos);
        }

        return content;
    }

    /**
     * 解析figure
     */
    private ThirdPartyResponseDto.AliyunResponse.AliyunFigure parseFigure(Map<String, Object> figureMap) {
        ThirdPartyResponseDto.AliyunResponse.AliyunFigure figure =
                new ThirdPartyResponseDto.AliyunResponse.AliyunFigure();

        figure.setType((String) figureMap.get("type"));
        figure.setX((Integer) figureMap.get("x"));
        figure.setY((Integer) figureMap.get("y"));
        figure.setW((Integer) figureMap.get("w"));
        figure.setH((Integer) figureMap.get("h"));

        // 解析box
        Map<String, Object> boxMap = (Map<String, Object>) figureMap.get("box");
        if (boxMap != null) {
            ThirdPartyResponseDto.AliyunResponse.AliyunBox box =
                    new ThirdPartyResponseDto.AliyunResponse.AliyunBox();
            box.setX((Integer) boxMap.get("x"));
            box.setY((Integer) boxMap.get("y"));
            box.setW((Integer) boxMap.get("w"));
            box.setH((Integer) boxMap.get("h"));
            box.setAngle((Integer) boxMap.get("angle"));
            figure.setBox(box);
        }

        // 解析points
        List<Map<String, Object>> pointMaps = (List<Map<String, Object>>) figureMap.get("points");
        if (pointMaps != null) {
            List<ThirdPartyResponseDto.AliyunResponse.AliyunPoint> points =
                    pointMaps.stream()
                            .map(this::parsePoint)
                            .collect(Collectors.toList());
            figure.setPoints(points);
        }

        return figure;
    }

    /**
     * 解析point
     */
    private ThirdPartyResponseDto.AliyunResponse.AliyunPoint parsePoint(Map<String, Object> pointMap) {
        ThirdPartyResponseDto.AliyunResponse.AliyunPoint point =
                new ThirdPartyResponseDto.AliyunResponse.AliyunPoint();
        point.setX(getIntegerValue(pointMap, "x"));
        point.setY(getIntegerValue(pointMap, "y"));
        return point;
    }

    /**
     * 安全地获取整数值
     */
    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            log.warn("无法解析整数值: key={}, value={}", key, value);
            return null;
        }
    }

    /**
     * 安全地获取Double值
     */
    private Double getDoubleValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Double) {
            return (Double) value;
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            log.warn("无法解析Double值: key={}, value={}", key, value);
            return null;
        }
    }
}
