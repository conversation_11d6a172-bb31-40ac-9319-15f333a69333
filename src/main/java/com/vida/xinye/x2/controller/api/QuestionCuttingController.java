package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.dto.BatchQuestionCuttingResultDto;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import com.vida.xinye.x2.dto.param.QuestionCuttingRequest;
import com.vida.xinye.x2.service.QuestionCuttingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 智能切题控制器（简化版）
 * 统一所有切题接口，大幅减少代码重复
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
@Slf4j
@RestController
@RequestMapping("/api/question-cutting")
public class QuestionCuttingController {

    @Autowired
    private QuestionCuttingService questionCuttingService;

    /**
     * 统一切题接口 - 支持文件上传
     */
    @PostMapping(value = "/cut", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public QuestionCuttingResultDto cutQuestionByFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "strategy", required = false) String strategy,
            @RequestParam(value = "provider", required = false) String provider,
            @RequestParam(value = "subject", required = false) String subject,
            @RequestParam(value = "grade", required = false) String grade,
            @RequestParam(value = "needDetail", required = false, defaultValue = "false") Boolean needDetail,
            @RequestParam(value = "enableCache", required = false, defaultValue = "true") Boolean enableCache,
            @RequestParam(value = "timeout", required = false) Integer timeout) {

        QuestionCuttingRequest request = QuestionCuttingRequest.fromFile(file)
                .strategy(strategy)
                .provider(provider)
                .subject(subject)
                .grade(grade)
                .needDetail(needDetail)
                .enableCache(enableCache)
                .timeout(timeout);

        return questionCuttingService.cutQuestion(request);
    }

    /**
     * 统一切题接口 - JSON方式（支持URL和Base64）
     */
    @PostMapping(value = "/cut", consumes = MediaType.APPLICATION_JSON_VALUE)
    public QuestionCuttingResultDto cutQuestionByJson(
            @Valid @RequestBody QuestionCuttingRequest request) {

        return questionCuttingService.cutQuestion(request);
    }

    /**
     * 批量切题接口
     */
    @PostMapping(value = "/batch", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BatchQuestionCuttingResultDto batchCutQuestion(
            @RequestParam("files") List<MultipartFile> files,
            @RequestParam(value = "strategy", required = false) String strategy,
            @RequestParam(value = "provider", required = false) String provider,
            @RequestParam(value = "subject", required = false) String subject,
            @RequestParam(value = "grade", required = false) String grade,
            @RequestParam(value = "needDetail", required = false, defaultValue = "false") Boolean needDetail,
            @RequestParam(value = "enableCache", required = false, defaultValue = "true") Boolean enableCache,
            @RequestParam(value = "parallel", required = false, defaultValue = "true") Boolean parallel) {

        QuestionCuttingRequest template = new QuestionCuttingRequest()
                .strategy(strategy)
                .provider(provider)
                .subject(subject)
                .grade(grade)
                .needDetail(needDetail)
                .enableCache(enableCache);

        return questionCuttingService.batchCutQuestion(files, template, parallel);
    }

    // ========== 系统信息接口 ==========

    /**
     * 获取支持的提供商列表
     */
    @GetMapping("/providers")
    public List<String> getSupportedProviders() {
        return questionCuttingService.getSupportedProviders();
    }

    /**
     * 获取支持的策略列表
     */
    @GetMapping("/strategies")
    public List<String> getSupportedStrategies() {
        return questionCuttingService.getSupportedStrategies();
    }

    /**
     * 获取系统统计信息
     */
    @GetMapping("/stats")
    public Map<String, Object> getSystemStats() {
        return questionCuttingService.getSystemStats();
    }

    // ========== 便捷接口（向后兼容） ==========

    /**
     * 快速切题 - 使用最佳质量策略
     */
    @PostMapping(value = "/quick", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public QuestionCuttingResultDto quickCut(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "subject", required = false) String subject) {

        return questionCuttingService.cutQuestionByFile(file, "BEST_QUALITY", null);
    }

    /**
     * 指定提供商切题
     */
    @PostMapping(value = "/provider/{provider}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public QuestionCuttingResultDto cutWithProvider(
            @PathVariable String provider,
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "subject", required = false) String subject) {

        return questionCuttingService.cutQuestionByFile(file, "SPECIFIED", provider);
    }

    /**
     * URL方式切题
     */
    @PostMapping("/url")
    public QuestionCuttingResultDto cutByUrl(
            @RequestParam(value = "imageUrl", required = false) String imageUrl,
            @RequestParam(value = "strategy", required = false) String strategy,
            @RequestParam(value = "provider", required = false) String provider) {

        // 参数验证
        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("图片URL不能为空");
        }

        return questionCuttingService.cutQuestionByUrl(imageUrl, strategy, provider);
    }

    /**
     * Base64方式切题
     */
    @PostMapping("/base64")
    public QuestionCuttingResultDto cutByBase64(
            @RequestParam(value = "imageBase64", required = false) String imageBase64,
            @RequestParam(value = "strategy", required = false) String strategy,
            @RequestParam(value = "provider", required = false) String provider) {

        // 参数验证
        if (imageBase64 == null || imageBase64.trim().isEmpty()) {
            throw new IllegalArgumentException("Base64图片数据不能为空");
        }

        return questionCuttingService.cutQuestionByBase64(imageBase64, strategy, provider);
    }
}
