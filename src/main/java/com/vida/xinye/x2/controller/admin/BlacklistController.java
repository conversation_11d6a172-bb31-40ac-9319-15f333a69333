package com.vida.xinye.x2.controller.admin;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.service.BlacklistService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: IP黑名单管理控制器
 * @Author: zhangwenbin
 * @Date: 2024/11/05 16:50
 */
@ApiRestController
@RequestMapping("/admin/blacklist")
@Slf4j
public class BlacklistController {

  @Autowired
  private BlacklistService blacklistService;

  /**
   * 检查IP是否在黑名单中
   *
   * @param ip IP地址
   * @return CommonResult
   */
  @RequestMapping(value = "/check", method = RequestMethod.GET)
  @ResponseBody
  public CommonResult checkIp(@RequestParam(required = false) String ip) {
    // 参数验证
    if (ip == null || ip.trim().isEmpty()) {
      return CommonResult.validateFailed("IP地址不能为空");
    }

    boolean isBlacklisted = blacklistService.isBlacklisted(ip);
    Map<String, Object> result = new HashMap<>();
    result.put("ip", ip);
    result.put("isBlacklisted", isBlacklisted);

    return CommonResult.success(result, "查询成功");
  }

  /**
   * 添加IP到黑名单
   *
   * @param ip       IP地址
   * @param duration 封禁时长（分钟）
   * @param reason   封禁原因
   * @return CommonResult
   */
  @RequestMapping(value = "/add", method = RequestMethod.POST)
  @ResponseBody
  public CommonResult addToBlacklist(@RequestParam(required = false) String ip,
      @RequestParam(defaultValue = "60") int duration,
      @RequestParam(defaultValue = "Manual block") String reason) {
    // 参数验证
    if (ip == null || ip.trim().isEmpty()) {
      return CommonResult.validateFailed("IP地址不能为空");
    }

    try {
      blacklistService.addToBlacklist(ip, duration, reason);
      return CommonResult.success(null, "IP已加入黑名单");
    } catch (Exception e) {
      log.error("添加IP到黑名单失败: ip={}", ip, e);
      return CommonResult.failed("添加失败");
    }
  }

  /**
   * 从黑名单中移除IP
   *
   * @param ip IP地址
   * @return CommonResult
   */
  @RequestMapping(value = "/remove", method = RequestMethod.POST)
  @ResponseBody
  public CommonResult removeFromBlacklist(@RequestParam(required = false) String ip) {
    // 参数验证
    if (ip == null || ip.trim().isEmpty()) {
      return CommonResult.validateFailed("IP地址不能为空");
    }

    try {
      blacklistService.removeFromBlacklist(ip);
      return CommonResult.success(null, "IP已从黑名单移除");
    } catch (Exception e) {
      log.error("从黑名单移除IP失败: ip={}", ip, e);
      return CommonResult.failed("移除失败");
    }
  }

  /**
   * 获取所有黑名单IP
   *
   * @return CommonResult
   */
  @RequestMapping(value = "/list", method = RequestMethod.GET)
  @ResponseBody
  public CommonResult listBlacklistIps() {
    try {
      Map<String, Object> result = new HashMap<>();
      result.put("staticIps", blacklistService.getStaticBlacklistIps());
      result.put("dynamicIps", blacklistService.getDynamicBlacklistIps());

      return CommonResult.success(result, "查询成功");
    } catch (Exception e) {
      log.error("获取黑名单IP列表失败", e);
      return CommonResult.failed("查询失败");
    }
  }
}