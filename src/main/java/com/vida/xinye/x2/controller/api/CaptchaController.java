package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.annotation.BlacklistCheck;
import com.vida.xinye.x2.annotation.RateLimit;
import com.vida.xinye.x2.annotation.SignatureCheck;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.constant.LoginWayConstant;
import com.vida.xinye.x2.dto.param.SendEmailCaptchaDto;
import com.vida.xinye.x2.service.CaptchaService;
import com.vida.xinye.x2.service.SignatureService;
import com.vida.xinye.x2.util.PhoneUtil;
import com.vida.xinye.x2.validator.AccountSecurityValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 验证码控制器
 *
 * <AUTHOR>
 * @date 2024/11/4
 */
@ApiRestController
@RequestMapping("/captcha")
public class CaptchaController {

    @Autowired
    private CaptchaService captchaService;

    @Autowired
    private SignatureService signatureService;

    @Autowired
    private AccountSecurityValidator accountSecurityValidator;

    /**
     * 发送验证码接口
     *
     * @param phone 手机号
     * @return CommonResult
     */
    @SignatureCheck(expireTime = 300, message = "Invalid signature")
    @BlacklistCheck(checkType = BlacklistCheck.CheckType.IP, message = "Access denied", autoBlock = true, autoBlockThreshold = 20, autoBlockWindow = 5, autoBlockDuration = 30)
    @RateLimit(key = "captcha:send", time = 60, count = 3, limitType = RateLimit.LimitType.IP, message = "Too many requests, please try again later")
    @RequestMapping(value = "/send", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult sendCaptcha(@RequestParam(required = false) String phone) {
        // 参数验证
        if (StringUtils.isEmpty(phone)) {
            return CommonResult.failed("手机号不能为空！");
        }

        // 手机号格式验证
        if (!PhoneUtil.isValidChinesePhone(phone)) {
            return CommonResult.failed("手机号格式不正确！");
        }

        boolean result = captchaService.sendVerificationCode(phone);
        if (result) {
            return CommonResult.success(null, "发送验证码成功");
        } else {
            return CommonResult.failed("发送验证码失败！");
        }
    }

    /**
     * 发送邮箱验证码接口
     *
     * @param sendEmailCaptchaDto 发送邮箱验证码请求参数
     * @return CommonResult
     */
    @BlacklistCheck(checkType = BlacklistCheck.CheckType.IP, message = "Access denied", autoBlock = true, autoBlockThreshold = 20, autoBlockWindow = 5, autoBlockDuration = 30)
    @RateLimit(key = "email_captcha:send", time = 60, count = 3, limitType = RateLimit.LimitType.IP, message = "Too many requests, please try again later")
    @PostMapping("/email/send")
    @ResponseBody
    public CommonResult sendEmailCaptcha(@Valid @RequestBody SendEmailCaptchaDto sendEmailCaptchaDto) {
        // 验证用途参数
        if (!isValidPurpose(sendEmailCaptchaDto.getPurpose())) {
            return CommonResult.failed("无效的验证码用途！");
        }

        boolean result = captchaService.sendEmailCode(sendEmailCaptchaDto.getEmail(), sendEmailCaptchaDto.getPurpose());
        if (result) {
            return CommonResult.success(null, "邮箱验证码发送成功");
        } else {
            return CommonResult.failed("邮箱验证码发送失败！");
        }
    }

    /**
     * 验证验证码接口（支持手机号和邮箱）
     *
     * @param account 账号（手机号或邮箱）
     * @param code    验证码
     * @param purpose 用途（邮箱验证码需要）
     * @return CommonResult
     */
    @BlacklistCheck(checkType = BlacklistCheck.CheckType.IP, message = "Access denied", autoBlock = true, autoBlockThreshold = 50, autoBlockWindow = 10, autoBlockDuration = 60)
    @RateLimit(key = "captcha:verify", time = 60, count = 10, limitType = RateLimit.LimitType.IP, message = "Too many verification attempts, please try again later")
    @RequestMapping(value = "/verify", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult verifyCaptcha(@RequestParam(required = false) String account,
                                      @RequestParam(required = false) String phone,
                                      @RequestParam(required = false) String code,
                                      @RequestParam(defaultValue = "login") String purpose) {
        // 兼容处理：优先使用account，如果没有则使用phone
        String actualAccount = account;
        if (StringUtils.isEmpty(actualAccount) && !StringUtils.isEmpty(phone)) {
            actualAccount = phone;
        }

        // 参数验证
        if (StringUtils.isEmpty(actualAccount) || StringUtils.isEmpty(code)) {
            return CommonResult.failed("账号和验证码不能为空！");
        }

        try {
            boolean result;
            if (accountSecurityValidator.isValidEmail(actualAccount)) {
                // 邮箱验证码验证
                result = captchaService.verifyEmailCode(actualAccount, code, purpose);
            } else if (PhoneUtil.isValidChinesePhone(actualAccount)) {
                // 手机号验证码验证
                result = captchaService.verifyCode(actualAccount, code);
            } else {
                return CommonResult.failed("账号格式不正确！");
            }

            if (result) {
                return CommonResult.success(null, "验证码验证成功");
            } else {
                return CommonResult.failed("验证码错误或已过期！");
            }
        } catch (Exception e) {
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * 验证用途参数是否有效
     */
    private boolean isValidPurpose(String purpose) {
        return LoginWayConstant.CAPTCHA_PURPOSE_LOGIN.equals(purpose) ||
                LoginWayConstant.CAPTCHA_PURPOSE_REGISTER.equals(purpose) ||
                LoginWayConstant.CAPTCHA_PURPOSE_RESET_PASSWORD.equals(purpose) ||
                LoginWayConstant.CAPTCHA_PURPOSE_BIND.equals(purpose);
    }

}
