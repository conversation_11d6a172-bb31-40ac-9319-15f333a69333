package com.vida.xinye.x2.controller.api;

import cn.hutool.core.util.StrUtil;
import com.vida.xinye.x2.dto.param.LoginRequest;
import com.vida.xinye.x2.dto.result.AuthResult;
import com.vida.xinye.x2.mbg.model.User;
import com.vida.xinye.x2.service.AuthService;
import com.vida.xinye.x2.util.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 统一认证控制器
 * 整合所有登录相关接口
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
@Slf4j
@RestController
@RequestMapping("/auth")
public class AuthController {

    @Autowired
    private AuthService authService;

    /**
     * 统一登录接口
     * 支持所有登录方式的统一入口
     */
    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody LoginRequest request,
                                     HttpServletRequest httpRequest) {
        log.info("进入统一登录接口");
        // 设置客户端信息
        enrichRequestInfo(request, httpRequest);

        // 执行登录
        AuthResult result = authService.login(request);

        return result.toSimpleMap();
    }

    /**
     * 表单登录接口（兼容原有接口）
     */
    @PostMapping(value = "/login/form", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public Map<String, Object> loginByForm(
            @RequestParam(value = "account", required = false) String account,
            @RequestParam(value = "phone", required = false) String phone, // 兼容旧版本参数名
            @RequestParam(value = "password", required = false) String password,
            @RequestParam(value = "loginType", defaultValue = "byemail_password") String loginType,
            @RequestParam(value = "captcha", required = false) String captcha,
            @RequestParam(value = "imageCaptcha", required = false) String imageCaptcha,
            @RequestParam(value = "imageCaptchaKey", required = false) String imageCaptchaKey,
            HttpServletRequest httpRequest) {

        // 兼容处理：优先使用account，如果没有则使用phone
        String actualAccount = account;
        if ((actualAccount == null || actualAccount.trim().isEmpty()) && phone != null) {
            actualAccount = phone;
        }

        // 参数验证
        if (actualAccount == null || actualAccount.trim().isEmpty()) {
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("success", false);
            result.put("errorCode", "INVALID_PARAMETER");
            result.put("errorMessage", "账号不能为空");
            result.put("timestamp", new java.util.Date());
            return result;
        }

        LoginRequest request = new LoginRequest();
        request.setAccount(actualAccount); // 使用兼容处理后的账号
        request.setLoginType(loginType);

        if (StrUtil.isNotBlank(password)) {
            request.setPassword(password);
        }
        if (StrUtil.isNotBlank(captcha)) {
            request.setCaptcha(captcha);
        }
        if (StrUtil.isNotBlank(imageCaptcha)) {
            request.setImageCaptcha(imageCaptcha);
            request.setImageCaptchaKey(imageCaptchaKey);
        }

        enrichRequestInfo(request, httpRequest);

        AuthResult result = authService.login(request);
        return result.toSimpleMap();
    }

    /**
     * 刷新Token
     */
    @PostMapping("/refresh")
    public Map<String, Object> refreshToken(@RequestParam(value = "refreshToken", required = false) String refreshToken) {
        // 参数验证
        if (refreshToken == null || refreshToken.trim().isEmpty()) {
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("success", false);
            result.put("message", "刷新token不能为空");
            result.put("timestamp", new java.util.Date());
            return result;
        }

        AuthResult result = authService.refreshToken(refreshToken);
        return result.toSimpleMap();
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public Map<String, Object> logout(@RequestHeader(value = "Authorization", required = false) String authorization) {
        String token = extractToken(authorization);
        boolean success = authService.logout(token);

        Map<String, Object> result = new java.util.HashMap<>();
        result.put("success", success);
        result.put("message", success ? "登出成功" : "登出失败");
        result.put("timestamp", new java.util.Date());

        return result;
    }

    /**
     * 验证Token
     */
    @GetMapping("/validate")
    public Map<String, Object> validateToken(@RequestHeader(value = "Authorization", required = false) String authorization) {
        String token = extractToken(authorization);
        boolean valid = authService.validateToken(token);

        Map<String, Object> result = new java.util.HashMap<>();
        result.put("valid", valid);
        result.put("timestamp", new java.util.Date());

        if (valid) {
            // 返回用户基本信息
            User user = authService.getUserFromToken(token);
            if (user != null) {
                Map<String, Object> userInfo = new java.util.HashMap<>();
                userInfo.put("id", user.getId());
                userInfo.put("username", user.getUsername());
                userInfo.put("nickname", user.getNickname());
                userInfo.put("email", user.getEmail());
                result.put("userInfo", userInfo);
            }
        }

        return result;
    }

    /**
     * 发送验证码
     */
    @PostMapping("/captcha/send")
    public Map<String, Object> sendCaptcha(
            @RequestParam(value = "account", required = false) String account,
            @RequestParam(value = "phone", required = false) String phone, // 兼容旧版本参数名
            @RequestParam(value = "type", defaultValue = "login") String type) {

        // 兼容处理：优先使用account，如果没有则使用phone
        String actualAccount = account;
        if ((actualAccount == null || actualAccount.trim().isEmpty()) && phone != null) {
            actualAccount = phone;
        }

        // 参数验证
        if (actualAccount == null || actualAccount.trim().isEmpty()) {
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("success", false);
            result.put("message", "账号不能为空");
            result.put("timestamp", new java.util.Date());
            return result;
        }

        boolean success = authService.sendCaptcha(actualAccount, type);

        Map<String, Object> result = new java.util.HashMap<>();
        result.put("success", success);
        result.put("message", success ? "验证码发送成功" : "验证码发送失败");
        result.put("timestamp", new java.util.Date());

        return result;
    }

    /**
     * 验证验证码
     */
    @PostMapping("/captcha/validate")
    public Map<String, Object> validateCaptcha(
            @RequestParam(value = "account", required = false) String account,
            @RequestParam(value = "phone", required = false) String phone, // 兼容旧版本参数名
            @RequestParam(value = "captcha", required = true) String captcha,
            @RequestParam(value = "type", defaultValue = "login") String type) {

        // 兼容处理：优先使用account，如果没有则使用phone
        String actualAccount = account;
        if ((actualAccount == null || actualAccount.trim().isEmpty()) && phone != null) {
            actualAccount = phone;
        }

        // 参数验证
        if (actualAccount == null || actualAccount.trim().isEmpty()) {
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("valid", false);
            result.put("message", "账号不能为空");
            result.put("timestamp", new java.util.Date());
            return result;
        }

        if (captcha == null || captcha.trim().isEmpty()) {
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("valid", false);
            result.put("message", "验证码不能为空");
            result.put("timestamp", new java.util.Date());
            return result;
        }

        boolean valid = authService.validateCaptcha(actualAccount, captcha, type);

        Map<String, Object> result = new java.util.HashMap<>();
        result.put("valid", valid);
        result.put("message", valid ? "验证码正确" : "验证码错误或已过期");
        result.put("timestamp", new java.util.Date());

        return result;
    }

    // ========== 便捷登录接口（向后兼容） ==========

    /**
     * 邮箱密码登录
     */
    @PostMapping("/login/email")
    public Map<String, Object> loginByEmail(
            @RequestParam(value = "email", required = false) String email,
            @RequestParam(value = "password", required = false) String password,
            HttpServletRequest httpRequest) {

        // 参数验证
        if (email == null || email.trim().isEmpty()) {
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("success", false);
            result.put("message", "邮箱不能为空");
            result.put("timestamp", new java.util.Date());
            return result;
        }
        if (password == null || password.trim().isEmpty()) {
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("success", false);
            result.put("message", "密码不能为空");
            result.put("timestamp", new java.util.Date());
            return result;
        }

        String clientIp = IpUtils.getIpAddr(httpRequest);
        AuthResult result = authService.loginByEmailPassword(email, password, clientIp);
        return result.toSimpleMap();
    }

    /**
     * 手机验证码登录
     */
    @PostMapping("/login/phone")
    public Map<String, Object> loginByPhone(
            @RequestParam(value = "phone", required = false) String phone,
            @RequestParam(value = "captcha", required = false) String captcha,
            HttpServletRequest httpRequest) {

        // 参数验证
        if (phone == null || phone.trim().isEmpty()) {
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("success", false);
            result.put("message", "手机号不能为空");
            result.put("timestamp", new java.util.Date());
            return result;
        }
        if (captcha == null || captcha.trim().isEmpty()) {
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("success", false);
            result.put("message", "验证码不能为空");
            result.put("timestamp", new java.util.Date());
            return result;
        }

        String clientIp = IpUtils.getIpAddr(httpRequest);
        AuthResult result = authService.loginByPhoneCaptcha(phone, captcha, clientIp);
        return result.toSimpleMap();
    }

    /**
     * 第三方登录
     */
    @PostMapping("/login/third-party")
    public Map<String, Object> loginByThirdParty(
            @RequestParam(value = "loginType", required = false) String loginType,
            @RequestParam(value = "accessToken", required = false) String accessToken,
            @RequestParam(value = "openId", required = false) String openId,
            HttpServletRequest httpRequest) {

        // 参数验证
        if (loginType == null || loginType.trim().isEmpty()) {
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("success", false);
            result.put("message", "登录类型不能为空");
            result.put("timestamp", new java.util.Date());
            return result;
        }
        if (accessToken == null || accessToken.trim().isEmpty()) {
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("success", false);
            result.put("message", "访问令牌不能为空");
            result.put("timestamp", new java.util.Date());
            return result;
        }
        if (openId == null || openId.trim().isEmpty()) {
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("success", false);
            result.put("message", "OpenID不能为空");
            result.put("timestamp", new java.util.Date());
            return result;
        }

        String clientIp = IpUtils.getIpAddr(httpRequest);
        AuthResult result = authService.loginByThirdParty(loginType, accessToken, openId, clientIp);
        return result.toSimpleMap();
    }

    // ========== 系统信息接口 ==========

    /**
     * 获取支持的登录方式
     */
    @GetMapping("/login-types")
    public Map<String, Object> getSupportedLoginTypes() {
        List<String> loginTypes = authService.getSupportedLoginTypes();

        Map<String, Object> result = new java.util.HashMap<>();
        result.put("loginTypes", loginTypes);
        result.put("timestamp", new java.util.Date());

        return result;
    }

    /**
     * 获取认证统计信息
     */
    @GetMapping("/stats")
    public Map<String, Object> getAuthStats() {
        return authService.getAuthStats();
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> healthCheck() {
        return authService.healthCheck();
    }

    // ========== 辅助方法 ==========

    /**
     * 丰富请求信息
     */
    private void enrichRequestInfo(LoginRequest request, HttpServletRequest httpRequest) {
        if (StrUtil.isBlank(request.getClientIp())) {
            request.setClientIp(IpUtils.getIpAddr(httpRequest));
        }

        if (StrUtil.isBlank(request.getUserAgent())) {
            request.setUserAgent(httpRequest.getHeader("User-Agent"));
        }

        // 从Header中获取设备信息
        String deviceId = httpRequest.getHeader("X-Device-Id");
        if (StrUtil.isNotBlank(deviceId)) {
            request.setDeviceId(deviceId);
        }

        String deviceType = httpRequest.getHeader("X-Device-Type");
        if (StrUtil.isNotBlank(deviceType)) {
            request.setDeviceType(deviceType);
        }

        String appVersion = httpRequest.getHeader("X-App-Version");
        if (StrUtil.isNotBlank(appVersion)) {
            request.setAppVersion(appVersion);
        }

        String channel = httpRequest.getHeader("X-Channel");
        if (StrUtil.isNotBlank(channel)) {
            request.setChannel(channel);
        }
    }

    /**
     * 从Authorization头中提取Token
     */
    private String extractToken(String authorization) {
        if (StrUtil.isBlank(authorization)) {
            return null;
        }

        if (authorization.startsWith("Bearer ")) {
            return authorization.substring(7);
        }

        return authorization;
    }
}
