package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.dto.SystemTempletGroupDto;
import com.vida.xinye.x2.service.SysTempletGroupService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.List;

@ApiRestController
@RequestMapping("/system/templet-group")
public class SystemTempletGroupController {
    @Resource
    private SysTempletGroupService sysTempletGroupService;

    @GetMapping
    public CommonResult<List<SystemTempletGroupDto>> list(@RequestParam(required = false) String locale) {
        // 参数验证
        if (locale == null || locale.trim().isEmpty()) {
            return CommonResult.validateFailed("语言环境参数不能为空");
        }
        return CommonResult.success(sysTempletGroupService.listGroups(locale));
    }

}

