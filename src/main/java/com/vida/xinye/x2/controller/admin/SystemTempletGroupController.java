package com.vida.xinye.x2.controller.admin;

import com.vida.xinye.x2.annotation.AdminRestController;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.dao.TempletGroupDao;
import com.vida.xinye.x2.dto.SystemTempletGroupDto;
import com.vida.xinye.x2.dto.param.SysTempletGroupParam;
import com.vida.xinye.x2.service.SysTempletGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@AdminRestController("adminSystemTempletGroupController")
@RequestMapping("/system/templet-group")
public class SystemTempletGroupController {

    @Autowired
    private SysTempletGroupService sysTempletGroupService;
    @Resource
    private TempletGroupDao templetGroupDao;

    @GetMapping
    public CommonResult<List<SystemTempletGroupDto>> list(@RequestParam(required = false) String locale) {
        // 参数验证
        if (locale == null || locale.trim().isEmpty()) {
            return CommonResult.validateFailed("语言环境参数不能为空");
        }
        return CommonResult.success(sysTempletGroupService.listGroups(locale));
    }

    @PostMapping
    public CommonResult<Long> create(@Validated @RequestBody SysTempletGroupParam param) {
        return CommonResult.success(sysTempletGroupService.createGroup(param));
    }

    @PutMapping("/{id}")
    public CommonResult update(@PathVariable Long id, @Validated @RequestBody SysTempletGroupParam param) {
        return sysTempletGroupService.updateGroup(id, param) > 0 ?
                CommonResult.success(null, "修改成功") : CommonResult.failed();
    }

    @DeleteMapping("/{id}")
    public CommonResult delete(@PathVariable Long id) {
        return sysTempletGroupService.deleteGroup(id) > 0 ?
                CommonResult.success(null, "删除成功") : CommonResult.failed();
    }

    @PostMapping("/sort")
    public CommonResult sort(@RequestBody List<Long> groupIds) {
        return sysTempletGroupService.sortGroups(groupIds) > 0 ?
                CommonResult.success(null, "排序成功") : CommonResult.failed();
    }
}

