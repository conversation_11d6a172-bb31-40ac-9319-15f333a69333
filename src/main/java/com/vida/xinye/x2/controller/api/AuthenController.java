package com.vida.xinye.x2.controller.api;

import cn.hutool.core.util.BooleanUtil;
import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.annotation.OperationLog;
import com.vida.xinye.x2.annotation.RateLimit;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.constant.LoginWayConstant;
import com.vida.xinye.x2.constant.OperationLogTypeEnum;
import com.vida.xinye.x2.dto.TokenDto;
import com.vida.xinye.x2.dto.param.ResetPwdDto;
import com.vida.xinye.x2.dto.param.UserAuthenDto;
import com.vida.xinye.x2.dto.param.IdCardVerifyDto;
import com.vida.xinye.x2.dto.IdCardVerifyResultDto;
import com.vida.xinye.x2.dto.param.SmsLoginDto;
import com.vida.xinye.x2.dto.param.EmailLoginDto;
import com.vida.xinye.x2.dto.param.PhoneLoginDto;
import com.vida.xinye.x2.dto.param.PhonePasswordLoginDto;
import com.vida.xinye.x2.dto.param.ThirdPartyLoginDto;
import com.vida.xinye.x2.service.CaptchaService;
import com.vida.xinye.x2.service.ExtendedAuthService;
import com.vida.xinye.x2.service.api.AuthService;
import com.vida.xinye.x2.util.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 身份认证相关接口
 * 整合了原有登录方式和新增的登录方式
 *
 * <AUTHOR>
 * @date 2024/11/4
 */
@ApiRestController
@RequestMapping("/authen")
@Slf4j
public class AuthenController {

    @Autowired
    private AuthService authService;

    @Autowired
    private ExtendedAuthService extendedAuthService;

    @Autowired
    private CaptchaService captchaService;

    @Autowired
    private MessageSource messageSource;

    /**
     * 用户登录
     * 登录以后返回token
     */
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult login(@RequestBody UserAuthenDto loginDto) {
        TokenDto tokenDto = authService.login(loginDto);
        if (tokenDto == null) {
            return CommonResult.failed("登录失败！");
        }
        return CommonResult.success(tokenDto, "登录成功");
    }

    /**
     * 用户退出登录
     * 登录以后返回token
     */
    @OperationLog(category = "用户认证", subcategory = "登录登出", desc = "用户退出登录", type = OperationLogTypeEnum.UPDATE)
    @RequestMapping(value = "/logout", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult logout() {

        return CommonResult.success(null);
    }

    /**
     * 注册接口
     *
     * @return CommonResult
     * @RequestBody UserAuthenDto registerDto
     */
    @RequestMapping(value = "/register", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult register(@Validated @RequestBody UserAuthenDto registerDto) {
        TokenDto tokenDto = authService.register(registerDto);
        if (tokenDto == null) {
            return CommonResult.failed("注册失败！");
        }
        return CommonResult.success(tokenDto, "注册成功");
    }

    /**
     * 重置密码接口
     *
     * @return CommonResult
     * @RequestBody UserAuthenDto registerDto
     */
    @RequestMapping(value = "/resetPwd", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult resetPwd(@Validated @RequestBody ResetPwdDto resetPwdDto) {
        boolean result = authService.resetPwd(resetPwdDto);
        if (BooleanUtil.isTrue(result)) {
            return CommonResult.success(null, "修改密码成功!");
        }
        return CommonResult.failed("修改密码失败！");
    }

    /**
     * 身份证实名认证
     * 调用阿里云云市场接口进行身份证验证
     *
     * @param verifyDto 身份证验证参数
     * @return IdCardVerifyResultDto
     */
    @OperationLog(category = "用户认证", subcategory = "身份证验证", desc = "用户身份证实名验证", type = OperationLogTypeEnum.CREATE)
    @PostMapping("/idcard/verify")
    @ResponseBody
    @RateLimit(key = "idcard_verify", time = 1, timeUnit = java.util.concurrent.TimeUnit.MINUTES, count = 3, limitType = RateLimit.LimitType.USER, message = "rate.limit.idcard.verify.frequent")
    public IdCardVerifyResultDto verifyIdCard(@Validated @RequestBody IdCardVerifyDto verifyDto) {
        return authService.verifyIdCard(verifyDto);
    }

    /**
     * 手机号+验证码登录，自动注册
     * 支持JSON和表单两种格式
     *
     * @param smsLoginDto 登录参数（JSON格式）
     * @param phone       手机号（表单格式）
     * @param account     账号（表单格式，兼容旧版本）
     * @param smsCode     验证码（表单格式）
     * @param captcha     验证码（表单格式，兼容字段名）
     * @return CommonResult
     */
    @RequestMapping(value = "/loginBySms", method = RequestMethod.POST)
    @ResponseBody
    @OperationLog(category = "用户认证", subcategory = "手机短信", desc = "手机短信登录", type = OperationLogTypeEnum.GET)
    public CommonResult loginBySms(@Validated @RequestBody SmsLoginDto smsLoginDto) {
        log.warn("进入手机短信登录功能-loginBySms");
        TokenDto tokenDto = authService.loginBySms(smsLoginDto);
        if (tokenDto == null) {
            return CommonResult.failed("登录失败！");
        }
        return CommonResult.success(tokenDto, "登录成功");
    }


    /**
     * 刷新token接口（兼容接口）
     * 当token过期时，前端可调用此接口获取新的token
     *
     * @param refreshToken 刷新token
     * @return CommonResult
     * @deprecated 建议使用 /auth/token/refresh 接口，该接口提供更完善的安全机制
     */
    @Deprecated
    @RequestMapping(value = "/refresh", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult refreshToken(@RequestParam String refreshToken) {
        log.warn("使用了已废弃的Token刷新接口 /authen/refresh，建议迁移到 /auth/token/refresh");

        TokenDto tokenDto = authService.refreshTokenByExpiredToken(refreshToken);
        if (tokenDto == null) {
            String msg = messageSource.getMessage("token.refresh.failed", null, "刷新token失败！",
                    LocaleContextHolder.getLocale());
            return CommonResult.failed(msg);
        }
        String msg = messageSource.getMessage("token.refresh.success", null, "刷新token成功",
                LocaleContextHolder.getLocale());
        return CommonResult.success(tokenDto, msg);
    }

    // ==================== 新增登录方式 ====================

    /**
     * 邮箱登录（验证码或密码）
     */
    @OperationLog(category = "用户认证", subcategory = "邮箱登录", desc = "用户邮箱登录", type = OperationLogTypeEnum.CREATE)
    @RateLimit(key = "email_login", time = 60, count = 10, limitType = RateLimit.LimitType.IP)
    @PostMapping("/email/login")
    public CommonResult<TokenDto> emailLogin(@Validated @RequestBody EmailLoginDto emailLoginDto,
                                             HttpServletRequest request) {
        try {
            log.info("邮箱登录请求 - 邮箱: {}, 登录方式: {}, IP: {}",
                    emailLoginDto.getEmail(), emailLoginDto.getLoginMethod(), IpUtils.getIpAddr(request));

            TokenDto tokenDto = extendedAuthService.emailLogin(emailLoginDto);
            return CommonResult.success(tokenDto, "登录成功");

        } catch (Exception e) {
            log.error("邮箱登录失败 - 邮箱: {}", emailLoginDto.getEmail(), e);
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * 手机号登录（密码或验证码）- 推荐使用
     */
    @OperationLog(category = "用户认证", subcategory = "手机号登录", desc = "用户手机号登录", type = OperationLogTypeEnum.CREATE)
    @RateLimit(key = "phone_login", time = 60, count = 10, limitType = RateLimit.LimitType.IP)
    @PostMapping("/phone/login")
    public CommonResult<TokenDto> phoneLogin(@Validated @RequestBody PhoneLoginDto phoneLoginDto,
                                             HttpServletRequest request) {
        try {
            log.info("手机号登录请求 - 手机号: {}, 登录方式: {}, IP: {}",
                    phoneLoginDto.getPhone(), phoneLoginDto.getLoginMethod(), IpUtils.getIpAddr(request));

            TokenDto tokenDto = extendedAuthService.phoneLogin(phoneLoginDto);
            return CommonResult.success(tokenDto, "登录成功");

        } catch (Exception e) {
            log.error("手机号登录失败 - 手机号: {}, 登录方式: {}",
                    phoneLoginDto.getPhone(), phoneLoginDto.getLoginMethod(), e);
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * 手机号密码登录（兼容接口）
     */
    @OperationLog(category = "用户认证", subcategory = "手机号密码登录", desc = "用户手机号密码登录", type = OperationLogTypeEnum.CREATE)
    @RateLimit(key = "phone_password_login", time = 60, count = 10, limitType = RateLimit.LimitType.IP)
    @PostMapping("/phone/password/login")
    public CommonResult<TokenDto> phonePasswordLogin(@Validated @RequestBody PhonePasswordLoginDto phonePasswordLoginDto,
                                                     HttpServletRequest request) {
        try {
            log.info("手机号密码登录请求 - 手机号: {}, IP: {}",
                    phonePasswordLoginDto.getPhone(), IpUtils.getIpAddr(request));

            TokenDto tokenDto = extendedAuthService.phonePasswordLogin(phonePasswordLoginDto);
            return CommonResult.success(tokenDto, "登录成功");

        } catch (Exception e) {
            log.error("手机号密码登录失败 - 手机号: {}", phonePasswordLoginDto.getPhone(), e);
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * 第三方登录 - APP端直接传递SDK获取的用户信息
     * <p>
     * APP端流程：
     * 1. APP调用第三方官方SDK（微信、QQ等）
     * 2. SDK返回用户信息（openId、unionId、昵称、头像等）
     * 3. APP直接调用此接口传递用户信息
     * 4. 后端根据openId/unionId查找用户，不存在则自动注册
     * <p>
     * 支持平台：微信(byweixin)、QQ(byqq)、微博(bysina)、Apple(byappleid)
     *
     * @param thirdPartyLoginDto 第三方登录参数
     * @param request            HTTP请求
     * @return 登录结果，包含JWT token和用户信息
     */
    @OperationLog(category = "用户认证", subcategory = "第三方登录", desc = "用户第三方登录", type = OperationLogTypeEnum.CREATE)
    @RateLimit(key = "third_party_login", time = 60, count = 10, limitType = RateLimit.LimitType.IP)
    @PostMapping("/third-party/login")
    public CommonResult<TokenDto> thirdPartyLogin(@Validated @RequestBody ThirdPartyLoginDto thirdPartyLoginDto,
                                                  HttpServletRequest request) {
        try {
            log.info("第三方登录请求 - 登录方式: {}, openId: {}, from: {}, IP: {}",
                    thirdPartyLoginDto.getLoginWay(),
                    maskSensitiveInfo(thirdPartyLoginDto.getOpenId()),
                    thirdPartyLoginDto.getFrom(),
                    IpUtils.getIpAddr(request));

            TokenDto tokenDto = extendedAuthService.thirdPartyLogin(thirdPartyLoginDto);

            log.info("第三方登录成功 - 用户ID: {}, 登录方式: {}, 是否需要绑定手机: {}",
                    tokenDto.getUserId(),
                    thirdPartyLoginDto.getLoginWay(),
                    tokenDto.getRequirePhoneBinding());

            return CommonResult.success(tokenDto, "登录成功");

        } catch (Exception e) {
            log.error("第三方登录失败 - 登录方式: {}, openId: {}, error: {}",
                    thirdPartyLoginDto.getLoginWay(),
                    maskSensitiveInfo(thirdPartyLoginDto.getOpenId()),
                    e.getMessage(), e);
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * 脱敏敏感信息用于日志记录
     */
    private String maskSensitiveInfo(String info) {
        if (info == null || info.length() <= 6) {
            return info;
        }
        return info.substring(0, 3) + "***" + info.substring(info.length() - 3);
    }


}