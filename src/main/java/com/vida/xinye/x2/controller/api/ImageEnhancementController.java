package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.domain.param.ImageEnhancementReqParam;
import com.vida.xinye.x2.dto.ImageEnhancementDto;
import com.vida.xinye.x2.service.ImageEnhancementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Base64;

/**
 * Author：Chenjy
 * Date:2025/8/18
 * Description:
 */
@ApiRestController
@RequestMapping("/image-ocr")
public class ImageEnhancementController {
    @Autowired
    private ImageEnhancementService imageEnhancementService;

    @PostMapping(value = "/enhance")
    public CommonResult enhanceImageJson(@RequestBody ImageEnhancementReqParam request) {
        try {
            // 验证Base64图片数据
            if (request.getImageBase64() == null || request.getImageBase64().trim().isEmpty()) {
                return CommonResult.failed("图片数据不能为空");
            }
            // 解码Base64图片
            byte[] imageData;
            try {
                imageData = Base64.getDecoder().decode(request.getImageBase64());
            } catch (IllegalArgumentException e) {
                return CommonResult.failed("图片Base64格式错误");
            }

            // 处理图像
            ImageEnhancementDto response = (request.getOptions() == null)
                    ? imageEnhancementService.enhanceImage(imageData)
                    : imageEnhancementService.recognize(imageData, request.getOptions());
            return CommonResult.success(response);


        } catch (IllegalArgumentException e) {
            return CommonResult.failed("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return CommonResult.failed("处理失败: " + e.getMessage());
        }
    }
}
