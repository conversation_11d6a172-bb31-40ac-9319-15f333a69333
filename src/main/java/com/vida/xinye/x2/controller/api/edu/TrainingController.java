package com.vida.xinye.x2.controller.api.edu;

import cn.hutool.core.util.ObjectUtil;
import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.domain.param.edu.AddTrainingQuestionParam;
import com.vida.xinye.x2.domain.param.xkw.KeywordSearchParam;
import com.vida.xinye.x2.domain.param.xkw.ScoreImprovementRecommendParam;
import com.vida.xinye.x2.dto.UserDto;
import com.vida.xinye.x2.dto.edu.*;
import com.vida.xinye.x2.mbg.model.*;
import com.vida.xinye.x2.service.TrainingService;
import com.vida.xinye.x2.service.XkwService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 提分训练
 *
 * <AUTHOR>
 * @date 2025/03/17
 */
@ApiRestController
@RequestMapping("/training")
public class TrainingController {
    @Autowired
    private TrainingService trainingService;
    @Autowired
    private XkwService xkwService;

    /**
     * 获取过滤后的学段列表
     *
     * @return 学段列表
     */
    @GetMapping("/stage")
    public CommonResult getFilteredStages() {
        List<XkwStage> stages = trainingService.getStages();
        return CommonResult.success(stages);
    }

    /**
     * 根据学段ID获取科目列表
     *
     * @param stageId 学段ID
     * @return 科目列表
     */
    @GetMapping("/course")
    public CommonResult getCourses(@RequestParam(required = false) Integer stageId,
            @RequestParam(required = false) Integer subjectId,
            @AuthenticationPrincipal UserDto userDto) {
        // 调试用
        if (ObjectUtil.isNull(userDto)) {
            userDto = new UserDto();
            userDto.setId(1L);
        }

        Long userId = userDto != null ? userDto.getId() : null;
        List<CourseWithWrongQuestionCountDTO> courses = trainingService.getCoursesWithWrongQuestionCount(stageId,
                subjectId, userId);
        return CommonResult.success(courses);
    }

    /**
     * 获取所有年级列表
     *
     * @return 年级列表
     */
    @GetMapping("/grade")
    public CommonResult getAllGrades() {
        return CommonResult.success(trainingService.getGrades());
    }

    /**
     * 根据课程ID获取教材版本列表
     *
     * @param courseId 课程ID
     * @return 教材版本列表
     */
    @GetMapping("/textbook-version")
    public CommonResult<List<XopTextbookVersion>> getTextbookVersions(
            @RequestParam(name = "courseId", required = true) Integer courseId) {
        List<XopTextbookVersion> pageData = xkwService.getTextbookVersions(courseId);
        return CommonResult.success(pageData);
    }

    /**
     * 根据课程、年级、教材版本查询教材列表
     *
     * @param courseId  课程 ID
     * @param gradeId   年级 ID
     * @param versionId 教材版本 ID
     * @return 教材列表
     */
    @GetMapping("/textbook")
    @ResponseBody
    public CommonResult<XopPageData<XopTextbook>> getTextbooks(@RequestParam(required = false) Integer courseId,
            @RequestParam(required = false) Integer gradeId,
            @RequestParam(required = false) Integer versionId,
            @RequestParam(name = "pageIndex", required = false, defaultValue = "1") Integer pageIndex,
            @RequestParam(name = "pageSize", required = false, defaultValue = "50") Integer pageSize) {
        XopPageData<XopTextbook> result = xkwService.getTextbooks(courseId, gradeId, versionId, pageIndex, pageSize);
        return CommonResult.success(result);
    }

    /**
     * 根据教材 ID 查询教材（章节）目录
     *
     * @param textbookId 教材 ID
     * @return 教材（章节）目录列表
     */
    @GetMapping("/textbook/catalog")
    @ResponseBody
    public CommonResult<List<XopTextbookCatalog>> getTextbookCatalogs(@RequestParam Integer textbookId) {
        List<XopTextbookCatalog> result = xkwService.getTextbookCatalog(textbookId);
        return CommonResult.success(result);
    }

    /**
     * 章节知识点推题
     *
     * @return CommonResult
     */
    @RequestMapping(value = "/score-improvement-recommend", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<XopQuestionPushVO>> scoreImprovementRecommend(
            @Validated @RequestBody ScoreImprovementRecommendParam param,
            @AuthenticationPrincipal UserDto userDto) {
        Long userId = userDto != null ? userDto.getId() : null;
        if (ObjectUtil.isNull(userId)) {
            return CommonResult.failed("用户未登录");
        }

        List<XopQuestionPushVO> result = trainingService.scoreImprovementRecommend(userId, param);
        return CommonResult.success(result);
    }

    /**
     * 关键词搜题
     *
     * @return CommonResult
     */
    @RequestMapping(value = "/keyword-search", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<XopQuestionRecommendRespVO> keywordSearch(@Validated @RequestBody KeywordSearchParam param) {
        XopQuestionRecommendRespVO result = xkwService.keywordSearch(param);
        return CommonResult.success(result);
    }

    /**
     * 根据课程ID查询考点（知识点）
     *
     * @param courseId 课程ID
     * @return 考点（知识点）列表
     */
    @GetMapping("/knowledge-points")
    public CommonResult<List<XopKnowledgePoint>> getKnowledgePoints(@RequestParam(required = false) Integer courseId) {
        if (courseId == null) {
            return CommonResult.validateFailed("课程ID不能为空");
        }
        List<XopKnowledgePoint> knowledgePoints = xkwService.getKnowledgePoints(courseId);
        return CommonResult.success(knowledgePoints);
    }

    /**
     * 查询用户的提分训练-错题本
     *
     * @param courseId 课程ID
     * @return 训练题列表
     */
    @GetMapping("/wrong-question")
    public CommonResult<CommonPage<TrainingQuestion>> getUserWrongQuestions(
            @RequestParam(required = false) Long courseId,
            @RequestParam(required = false) Integer difficultyLevel,
            @RequestParam(required = false) String typeId,
            @RequestParam(required = false) Integer year,
            @RequestParam(value = "saveTimeRange", required = false) String saveTimeRange,
            @RequestParam(value = "pageSize", defaultValue = "8") Integer pageSize,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @AuthenticationPrincipal UserDto userDto) {

        Long userId = userDto != null ? userDto.getId() : null;
        if (ObjectUtil.isNull(userId)) {
            return CommonResult.failed("用户未登录");
        }

        CommonPage<TrainingQuestion> wrongQuestions = trainingService.getUserTrainingQuestions(userId, courseId,
                difficultyLevel, typeId, year, saveTimeRange, pageNum, pageSize);
        return CommonResult.success(wrongQuestions);
    }

    /**
     * 添加训练题到错题本
     *
     * @param param   请求参数
     * @param userDto 当前登录用户
     * @return 添加的训练题
     */
    @PostMapping("/wrong-question")
    public CommonResult<TrainingQuestion> addTrainingQuestion(
            @Validated @RequestBody AddTrainingQuestionParam param,
            @AuthenticationPrincipal UserDto userDto) {
        Long userId = userDto != null ? userDto.getId() : null;
        if (ObjectUtil.isNull(userId)) {
            return CommonResult.failed("用户未登录");
        }

        param.setUserId(userId);
        TrainingQuestion trainingQuestion = trainingService.addTrainingQuestion(param);
        return CommonResult.success(trainingQuestion);
    }

    /**
     * 批量删除训练题
     *
     * @param ids     训练题ID列表
     * @param userDto 当前登录用户
     * @return 删除的记录数
     */
    @PostMapping("/wrong-question/batch-delete")
    public CommonResult<Integer> batchDelete(
            @RequestBody List<Long> ids,
            @AuthenticationPrincipal UserDto userDto) {
        // 调试用
        if (ObjectUtil.isNull(userDto)) {
            userDto = new UserDto();
            userDto.setId(1L);
        }

        Long userId = userDto != null ? userDto.getId() : null;
        int count = trainingService.deleteBatch(ids, userId);
        if (count > 0) {
            return CommonResult.success(count);
        }
        return CommonResult.failed();
    }

}