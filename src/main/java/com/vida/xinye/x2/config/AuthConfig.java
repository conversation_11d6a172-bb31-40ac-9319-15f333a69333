package com.vida.xinye.x2.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 统一认证配置
 * 简化原有的分散配置
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
@Data
@Component
@ConfigurationProperties(prefix = "auth")
public class AuthConfig {

    /**
     * JWT配置
     */
    private JwtConfig jwt = new JwtConfig();

    /**
     * 登录安全配置
     */
    private SecurityConfig security = new SecurityConfig();

    /**
     * 缓存配置
     */
    private CacheConfig cache = new CacheConfig();

    /**
     * 验证码配置
     */
    private CaptchaConfig captcha = new CaptchaConfig();

    /**
     * 第三方登录配置
     */
    private Map<String, ThirdPartyConfig> thirdParty;

    /**
     * JWT配置
     */
    @Data
    public static class JwtConfig {
        /**
         * JWT密钥
         */
        private String secret = "xinye-x2-jwt-secret-key-2025";

        /**
         * 访问令牌过期时间（秒）
         */
        private Long accessTokenExpiration = 3600L; // 1小时

        /**
         * 刷新令牌过期时间（秒）
         */
        private Long refreshTokenExpiration = 7200L; // 2小时

        /**
         * 令牌前缀
         */
        private String tokenPrefix = "Bearer ";

        /**
         * 令牌头名称
         */
        private String tokenHeader = "Authorization";

        /**
         * 签发者
         */
        private String issuer = "xinye-x2";

        /**
         * 受众
         */
        private String audience = "xinye-x2-users";
    }

    /**
     * 安全配置
     */
    @Data
    public static class SecurityConfig {
        /**
         * 最大登录失败次数
         */
        private Integer maxLoginAttempts = 5;

        /**
         * 账号锁定时间（分钟）
         */
        private Integer accountLockMinutes = 30;

        /**
         * 需要验证码的失败次数阈值
         */
        private Integer captchaThreshold = 3;

        /**
         * IP每日最大尝试次数
         */
        private Integer ipDailyMaxAttempts = 100;

        /**
         * 密码最小长度
         */
        private Integer passwordMinLength = 6;

        /**
         * 密码最大长度
         */
        private Integer passwordMaxLength = 20;

        /**
         * 是否启用设备绑定
         */
        private Boolean enableDeviceBinding = false;

        /**
         * 是否启用IP白名单
         */
        private Boolean enableIpWhitelist = false;

        /**
         * IP白名单
         */
        private String[] ipWhitelist = {};

        /**
         * 是否启用二次验证
         */
        private Boolean enableSecondaryAuth = false;
    }

    /**
     * 缓存配置
     */
    @Data
    public static class CacheConfig {
        /**
         * 用户信息缓存过期时间（秒）
         */
        private Integer userCacheExpiration = 3600; // 1小时

        /**
         * Token缓存过期时间（秒）
         */
        private Integer tokenCacheExpiration = 3600; // 1小时

        /**
         * 验证码缓存过期时间（秒）
         */
        private Integer captchaCacheExpiration = 300; // 5分钟

        /**
         * 登录失败记录过期时间（秒）
         */
        private Integer failureRecordExpiration = 86400; // 24小时

        /**
         * 是否启用缓存预热
         */
        private Boolean enableCacheWarmup = false;

        /**
         * 缓存键前缀
         */
        private String keyPrefix = "auth:";
    }

    /**
     * 验证码配置
     */
    @Data
    public static class CaptchaConfig {
        /**
         * 图形验证码长度
         */
        private Integer imageLength = 4;

        /**
         * 图形验证码宽度
         */
        private Integer imageWidth = 120;

        /**
         * 图形验证码高度
         */
        private Integer imageHeight = 40;

        /**
         * 图形验证码过期时间（秒）
         */
        private Integer imageExpiration = 300; // 5分钟

        /**
         * 短信验证码长度
         */
        private Integer smsLength = 6;

        /**
         * 短信验证码过期时间（秒）
         */
        private Integer smsExpiration = 300; // 5分钟

        /**
         * 邮箱验证码长度
         */
        private Integer emailLength = 6;

        /**
         * 邮箱验证码过期时间（秒）
         */
        private Integer emailExpiration = 300; // 5分钟

        /**
         * 每日最大发送次数
         */
        private Integer dailyMaxSendCount = 50;

        /**
         * 发送间隔（秒）
         */
        private Integer sendInterval = 60; // 1分钟

        /**
         * 是否启用图形验证码
         */
        private Boolean enableImageCaptcha = true;

        /**
         * 是否启用短信验证码
         */
        private Boolean enableSmsCaptcha = true;

        /**
         * 是否启用邮箱验证码
         */
        private Boolean enableEmailCaptcha = true;
    }

    /**
     * 第三方登录配置
     */
    @Data
    public static class ThirdPartyConfig {
        /**
         * 是否启用
         */
        private Boolean enabled = false;

        /**
         * 应用ID
         */
        private String appId;

        /**
         * 应用密钥
         */
        private String appSecret;

        /**
         * 回调地址
         */
        private String redirectUri;

        /**
         * 授权范围
         */
        private String scope;

        /**
         * 授权地址
         */
        private String authorizeUrl;

        /**
         * 获取Token地址
         */
        private String tokenUrl;

        /**
         * 获取用户信息地址
         */
        private String userInfoUrl;

        /**
         * 扩展配置
         */
        private Map<String, Object> extra;
    }

    // ========== 便捷方法 ==========

    /**
     * 获取第三方登录配置
     */
    public ThirdPartyConfig getThirdPartyConfig(String provider) {
        if (thirdParty == null) {
            return null;
        }
        return thirdParty.get(provider);
    }

    /**
     * 检查第三方登录是否启用
     */
    public boolean isThirdPartyEnabled(String provider) {
        ThirdPartyConfig config = getThirdPartyConfig(provider);
        return config != null && Boolean.TRUE.equals(config.getEnabled());
    }

    /**
     * 获取JWT完整密钥
     */
    public String getJwtFullSecret() {
        return jwt.getSecret() + "-" + System.currentTimeMillis() / 1000 / 3600 / 24; // 每天变化
    }

    /**
     * 获取Token完整前缀
     */
    public String getTokenFullPrefix() {
        return jwt.getTokenPrefix();
    }

    /**
     * 是否需要验证码
     */
    public boolean needCaptcha(int failureCount) {
        return failureCount >= security.getCaptchaThreshold();
    }

    /**
     * 是否需要锁定账号
     */
    public boolean needLockAccount(int failureCount) {
        return failureCount >= security.getMaxLoginAttempts();
    }

    /**
     * 获取账号锁定时长（秒）
     */
    public long getAccountLockDuration() {
        return security.getAccountLockMinutes() * 60L;
    }

    /**
     * 验证密码长度
     */
    public boolean isValidPasswordLength(String password) {
        if (password == null) {
            return false;
        }
        int length = password.length();
        return length >= security.getPasswordMinLength() && 
               length <= security.getPasswordMaxLength();
    }

    /**
     * 获取缓存键
     */
    public String getCacheKey(String suffix) {
        return cache.getKeyPrefix() + suffix;
    }
}
