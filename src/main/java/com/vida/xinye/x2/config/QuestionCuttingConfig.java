package com.vida.xinye.x2.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 智能切题配置类
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "question.cutting")
public class QuestionCuttingConfig {

    /**
     * 天壤接口配置
     */
    private TianrangConfig tianrang = new TianrangConfig();

    /**
     * 有道接口配置
     */
    private YoudaoConfig youdao = new YoudaoConfig();

    /**
     * 阿里云接口配置
     */
    private AliyunConfig aliyun = new AliyunConfig();

    /**
     * 通用配置
     */
    private CommonConfig common = new CommonConfig();

    @Data
    public static class TianrangConfig {
        /**
         * 接口地址
         */
        private String url;

        /**
         * API密钥
         */
        private String apiKey;

        /**
         * 超时时间（毫秒）
         */
        private int timeout;

        /**
         * 是否启用
         */
        private boolean enabled;

        /**
         * 重试次数
         */
        private int retryCount;
    }

    @Data
    public static class YoudaoConfig {
        /**
         * 接口地址
         */
        private String url;

        /**
         * 应用ID
         */
        private String appKey;

        /**
         * 应用密钥
         */
        private String appSecret;

        /**
         * 超时时间（毫秒）
         */
        private int timeout;

        /**
         * 是否启用
         */
        private boolean enabled;

        /**
         * 重试次数
         */
        private int retryCount;
    }

    @Data
    public static class AliyunConfig {
        /**
         * 接口地址
         */
        private String endpoint;

        /**
         * 访问密钥ID
         */
        private String accessKeyId;

        /**
         * 访问密钥Secret
         */
        private String accessKeySecret;

        /**
         * 区域ID（默认：cn-hangzhou）
         */
        private String regionId = "cn-hangzhou";

        /**
         * 产品代码（默认：ocr-api）
         */
        private String product = "ocr-api";

        /**
         * API版本（默认：2021-07-07）
         */
        private String version = "2021-07-07";

        /**
         * API动作（默认：RecognizeEduPaperStructed）
         */
        private String action = "RecognizeEduPaperStructed";

        /**
         * 是否需要自动旋转（默认：false）
         */
        private Boolean needRotate = false;

        /**
         * 是否输出原图坐标（默认：false）
         */
        private Boolean outputOricoord = false;

        /**
         * 超时时间（毫秒）
         */
        private int timeout;

        /**
         * 是否启用
         */
        private boolean enabled;

        /**
         * 重试次数
         */
        private int retryCount;
    }

    @Data
    public static class CommonConfig {
        /**
         * 默认切题策略
         */
        private String defaultStrategy;

        /**
         * 默认提供商（当strategy为SPECIFIED时使用）
         */
        private String defaultProvider;

        /**
         * 是否启用结果聚合
         */
        private boolean enableAggregation;

        /**
         * 最大并发请求数
         */
        private int maxConcurrentRequests;

        /**
         * 图片最大尺寸（字节）
         */
        private long maxImageSize;

        /**
         * 支持的图片格式
         */
        private String[] supportedFormats;

        /**
         * 是否启用缓存
         */
        private boolean enableCache;

        /**
         * 缓存过期时间（秒）
         */
        private int cacheExpireSeconds;
    }
}
