package com.vida.xinye.x2.dto;

import java.util.List;

/**
 * Author：Chenjy
 * Date:2025/8/18
 * Description: 图像增强响应 DTO - 基于实际 API 响应结构
 */
public class ImageEnhancementDto {
    private String xRequestId;
    private Integer code;
    private String message;
    private String version;
    private Long duration;
    private Result result;

    public ImageEnhancementDto() {
    }

    public ImageEnhancementDto(String xRequestId, Integer code, String message, String version, Long duration,
                                    Result result) {
        this.xRequestId = xRequestId;
        this.code = code;
        this.message = message;
        this.version = version;
        this.duration = duration;
        this.result = result;
    }

    /**
     * 响应结果对象
     */
    public static class Result {
        private Integer originWidth;
        private Integer originHeight;
        private List<ProcessedImage> imageList;

        public Result() {
        }

        public Result(Integer originWidth, Integer originHeight, List<ProcessedImage> imageList) {
            this.originWidth = originWidth;
            this.originHeight = originHeight;
            this.imageList = imageList;
        }

        // Getters and Setters
        public Integer getOriginWidth() {
            return originWidth;
        }

        public void setOriginWidth(Integer originWidth) {
            this.originWidth = originWidth;
        }

        public Integer getOriginHeight() {
            return originHeight;
        }

        public void setOriginHeight(Integer originHeight) {
            this.originHeight = originHeight;
        }

        public List<ProcessedImage> getImageList() {
            return imageList;
        }

        public void setImageList(List<ProcessedImage> imageList) {
            this.imageList = imageList;
        }
    }

    /**
     * 处理后的图片信息
     */
    public static class ProcessedImage {
        private Integer croppedWidth;
        private Integer croppedHeight;
        private String image; // Base64 格式的图片
        private List<Integer> position; // 切图区域的4个角点坐标，长度为8的数组
        private Integer angle; // 图像角度

        public ProcessedImage() {
        }

        public ProcessedImage(Integer croppedWidth, Integer croppedHeight, String image, List<Integer> position,
                              Integer angle) {
            this.croppedWidth = croppedWidth;
            this.croppedHeight = croppedHeight;
            this.image = image;
            this.position = position;
            this.angle = angle;
        }

        // Getters and Setters
        public Integer getCroppedWidth() {
            return croppedWidth;
        }

        public void setCroppedWidth(Integer croppedWidth) {
            this.croppedWidth = croppedWidth;
        }

        public Integer getCroppedHeight() {
            return croppedHeight;
        }

        public void setCroppedHeight(Integer croppedHeight) {
            this.croppedHeight = croppedHeight;
        }

        public String getImage() {
            return image;
        }

        public void setImage(String image) {
            this.image = image;
        }

        public List<Integer> getPosition() {
            return position;
        }

        public void setPosition(List<Integer> position) {
            this.position = position;
        }

        public Integer getAngle() {
            return angle;
        }

        public void setAngle(Integer angle) {
            this.angle = angle;
        }

        /**
         * 获取角点坐标的便捷方法
         */
        public CornerPoints getCornerPoints() {
            if (position == null || position.size() != 8) {
                return null;
            }
            return new CornerPoints(
                    position.get(0), position.get(1), // 左上角
                    position.get(2), position.get(3), // 右上角
                    position.get(4), position.get(5), // 右下角
                    position.get(6), position.get(7) // 左下角
            );
        }

        /**
         * 获取角度描述
         */
        public String getAngleDescription() {
            if (angle == null)
                return "未知";
            switch (angle) {
                case 0:
                    return "正置 (▲)";
                case 90:
                    return "顺时针90度 (▶)";
                case 180:
                    return "顺时针180度 (▼)";
                case 270:
                    return "顺时针270度 (◀)";
                case -1:
                    return "检测失败";
                default:
                    return "未知角度 (" + angle + "°)";
            }
        }
    }

    /**
     * 角点坐标类
     */
    public static class CornerPoints {
        private int topLeftX, topLeftY;
        private int topRightX, topRightY;
        private int bottomRightX, bottomRightY;
        private int bottomLeftX, bottomLeftY;

        public CornerPoints(int topLeftX, int topLeftY, int topRightX, int topRightY,
                            int bottomRightX, int bottomRightY, int bottomLeftX, int bottomLeftY) {
            this.topLeftX = topLeftX;
            this.topLeftY = topLeftY;
            this.topRightX = topRightX;
            this.topRightY = topRightY;
            this.bottomRightX = bottomRightX;
            this.bottomRightY = bottomRightY;
            this.bottomLeftX = bottomLeftX;
            this.bottomLeftY = bottomLeftY;
        }

        // Getters
        public int getTopLeftX() {
            return topLeftX;
        }

        public int getTopLeftY() {
            return topLeftY;
        }

        public int getTopRightX() {
            return topRightX;
        }

        public int getTopRightY() {
            return topRightY;
        }

        public int getBottomRightX() {
            return bottomRightX;
        }

        public int getBottomRightY() {
            return bottomRightY;
        }

        public int getBottomLeftX() {
            return bottomLeftX;
        }

        public int getBottomLeftY() {
            return bottomLeftY;
        }

        @Override
        public String toString() {
            return String.format("左上(%d,%d) 右上(%d,%d) 右下(%d,%d) 左下(%d,%d)",
                    topLeftX, topLeftY, topRightX, topRightY,
                    bottomRightX, bottomRightY, bottomLeftX, bottomLeftY);
        }
    }

    // Getters and Setters
    public String getXRequestId() {
        return xRequestId;
    }

    public void setXRequestId(String xRequestId) {
        this.xRequestId = xRequestId;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public Result getResult() {
        return result;
    }

    public void setResult(Result result) {
        this.result = result;
    }

    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return code != null && code == 0;
    }

}
