package com.vida.xinye.x2.dto.param;

import com.vida.xinye.x2.constant.LoginWayConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 统一登录请求对象
 * 支持所有登录方式的统一入口
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
@Data
public class LoginRequest {

    // ========== 基础信息 ==========
    
    /**
     * 账号（邮箱/手机号/用户名）
     */
//    @NotBlank(message = "账号不能为空")
    private String account;

    /**
     * 登录类型
     * 支持：bymobile, bymobile_password, bysms, byemail, byemail_password, 
     *      byweixin, byqq, bysina, byappleid, byyumeng
     */
    @NotBlank(message = "登录类型不能为空")
    private String loginType;

    // ========== 认证信息 ==========
    
    /**
     * 密码（密码登录时使用）
     */
    private String password;

    /**
     * 验证码（验证码登录时使用）
     */
    private String captcha;

    /**
     * 图形验证码（安全验证时使用）
     */
    private String imageCaptcha;

    /**
     * 图形验证码Key
     */
    private String imageCaptchaKey;

    // ========== 第三方登录信息 ==========
    
    /**
     * 第三方访问令牌
     */
    private String accessToken;

    /**
     * 第三方用户唯一标识
     */
    private String openId;

    /**
     * 第三方应用ID
     */
    private String appId;

    /**
     * 第三方unionId
     */
    private String unionId;

    /**
     * 来源标识（app, miniprogram, web）
     */
    private String from;

    /**
     * Apple ID的JWT Token
     */
    private String jwtToken;

    // ========== 用户信息（第三方登录可选） ==========
    
    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户头像
     */
    private String headPic;

    /**
     * 性别
     */
    private String sex;

    // ========== 设备和环境信息 ==========
    
    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 设备类型（ios, android, web）
     */
    private String deviceType;

    /**
     * 应用版本
     */
    private String appVersion;

    /**
     * 渠道来源
     */
    private String channel;

    // ========== 扩展参数 ==========
    
    /**
     * 扩展参数
     */
    private Map<String, Object> extraParams;

    // ========== 便捷构造方法 ==========

    /**
     * 创建邮箱密码登录请求
     */
    public static LoginRequest emailPassword(String email, String password) {
        LoginRequest request = new LoginRequest();
        request.setAccount(email);
        request.setPassword(password);
        request.setLoginType(LoginWayConstant.LOGIN_BY_EMAIL_PASSWORD);
        return request;
    }

    /**
     * 创建邮箱验证码登录请求
     */
    public static LoginRequest emailCaptcha(String email, String captcha) {
        LoginRequest request = new LoginRequest();
        request.setAccount(email);
        request.setCaptcha(captcha);
        request.setLoginType(LoginWayConstant.LOGIN_BY_EMAIL);
        return request;
    }

    /**
     * 创建手机密码登录请求
     */
    public static LoginRequest phonePassword(String phone, String password) {
        LoginRequest request = new LoginRequest();
        request.setAccount(phone);
        request.setPassword(password);
        request.setLoginType(LoginWayConstant.LOGIN_BY_MOBILE_PASSWORD);
        return request;
    }

    /**
     * 创建手机验证码登录请求
     */
    public static LoginRequest phoneCaptcha(String phone, String captcha) {
        LoginRequest request = new LoginRequest();
        request.setAccount(phone);
        request.setCaptcha(captcha);
        request.setLoginType(LoginWayConstant.LOGIN_BY_SMS);
        return request;
    }

    /**
     * 创建第三方登录请求
     */
    public static LoginRequest thirdParty(String loginType, String accessToken, String openId) {
        LoginRequest request = new LoginRequest();
        request.setLoginType(loginType);
        request.setAccessToken(accessToken);
        request.setOpenId(openId);
        return request;
    }

    // ========== 链式配置方法 ==========

    public LoginRequest clientIp(String clientIp) {
        this.clientIp = clientIp;
        return this;
    }

    public LoginRequest userAgent(String userAgent) {
        this.userAgent = userAgent;
        return this;
    }

    public LoginRequest deviceInfo(String deviceId, String deviceType) {
        this.deviceId = deviceId;
        this.deviceType = deviceType;
        return this;
    }

    public LoginRequest appInfo(String appVersion, String channel) {
        this.appVersion = appVersion;
        this.channel = channel;
        return this;
    }

    public LoginRequest imageCaptcha(String imageCaptcha, String imageCaptchaKey) {
        this.imageCaptcha = imageCaptcha;
        this.imageCaptchaKey = imageCaptchaKey;
        return this;
    }

    public LoginRequest extraParam(String key, Object value) {
        if (this.extraParams == null) {
            this.extraParams = new java.util.HashMap<>();
        }
        this.extraParams.put(key, value);
        return this;
    }

    // ========== 验证方法 ==========

    /**
     * 验证请求参数
     */
    public void validate() {
        if (account == null || account.trim().isEmpty()) {
            throw new IllegalArgumentException("账号不能为空");
        }

        if (loginType == null || loginType.trim().isEmpty()) {
            throw new IllegalArgumentException("登录类型不能为空");
        }

        // 根据登录类型验证必需参数
        switch (loginType) {
            case LoginWayConstant.LOGIN_BY_EMAIL_PASSWORD:
            case LoginWayConstant.LOGIN_BY_MOBILE_PASSWORD:
                if (password == null || password.trim().isEmpty()) {
                    throw new IllegalArgumentException("密码不能为空");
                }
                break;

            case LoginWayConstant.LOGIN_BY_EMAIL:
            case LoginWayConstant.LOGIN_BY_SMS:
            case LoginWayConstant.LOGIN_BY_MOBILE:
                if (captcha == null || captcha.trim().isEmpty()) {
                    throw new IllegalArgumentException("验证码不能为空");
                }
                break;

            case LoginWayConstant.LOGIN_BY_WEIXIN:
            case LoginWayConstant.LOGIN_BY_QQ:
            case LoginWayConstant.LOGIN_BY_SINA:
                if (accessToken == null || accessToken.trim().isEmpty()) {
                    throw new IllegalArgumentException("访问令牌不能为空");
                }
                break;

            case LoginWayConstant.LOGIN_BY_APPLE_ID:
                if (jwtToken == null || jwtToken.trim().isEmpty()) {
                    throw new IllegalArgumentException("JWT令牌不能为空");
                }
                break;
        }
    }

    /**
     * 是否为密码登录
     */
    public boolean isPasswordLogin() {
        return LoginWayConstant.LOGIN_BY_EMAIL_PASSWORD.equals(loginType) ||
               LoginWayConstant.LOGIN_BY_MOBILE_PASSWORD.equals(loginType);
    }

    /**
     * 是否为验证码登录
     */
    public boolean isCaptchaLogin() {
        return LoginWayConstant.LOGIN_BY_EMAIL.equals(loginType) ||
               LoginWayConstant.LOGIN_BY_SMS.equals(loginType) ||
               LoginWayConstant.LOGIN_BY_MOBILE.equals(loginType);
    }

    /**
     * 是否为第三方登录
     */
    public boolean isThirdPartyLogin() {
        return LoginWayConstant.LOGIN_BY_WEIXIN.equals(loginType) ||
               LoginWayConstant.LOGIN_BY_QQ.equals(loginType) ||
               LoginWayConstant.LOGIN_BY_SINA.equals(loginType) ||
               LoginWayConstant.LOGIN_BY_APPLE_ID.equals(loginType) ||
               LoginWayConstant.LOGIN_BY_YM_ONEKEY.equals(loginType);
    }

    /**
     * 获取账号类型
     */
    public String getAccountType() {
        if (account == null) {
            return "unknown";
        }
        
        if (account.contains("@")) {
            return "email";
        } else if (account.matches("^1[3-9]\\d{9}$")) {
            return "phone";
        } else {
            return "username";
        }
    }
}
