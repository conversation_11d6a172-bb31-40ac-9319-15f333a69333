package com.vida.xinye.x2.dto.internal;

import com.vida.xinye.x2.api.IErrorCode;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 第三方接口响应DTO
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Data
public class ThirdPartyResponseDto {

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误码
     */
    private IErrorCode errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 响应时间（毫秒）
     */
    private Long responseTime;

    /**
     * 原始响应数据
     */
    private Object rawData;

    /**
     * 天壤接口响应
     */
    @Data
    public static class TianrangResponse extends ThirdPartyResponseDto {
        /**
         * 状态码
         */
        private Integer code;

        /**
         * 消息
         */
        private String message;

        /**
         * 数据
         */
        private TianrangData data;

        @Data
        public static class TianrangData {
            /**
             * 检测结果
             */
            private List<TianrangQuestion> questions;

            /**
             * 图片高度
             */
            private Integer height;

            /**
             * 图片宽度
             */
            private Integer width;

            /**
             * 处理时间（秒）
             */
            private Double elapsedTime;

            /**
             * 总体置信度（计算得出）
             */
            private Double confidence;
        }

        @Data
        public static class TianrangQuestion {
            /**
             * 题目ID
             */
            private String questionId;

            /**
             * 题目类型
             */
            private String type;

            /**
             * 题目内容
             */
            private String content;

            /**
             * 选项
             */
            private List<String> options;

            /**
             * 答案
             */
            private String answer;

            /**
             * 位置信息
             */
            private BoundingBox bbox;

            /**
             * 置信度
             */
            private Double confidence;

            @Data
            public static class BoundingBox {
                private Integer x;
                private Integer y;
                private Integer width;
                private Integer height;

                /**
                 * 原始坐标数组 [x1,y1,x2,y2,x3,y3,x4,y4]
                 */
                private List<Integer> coordinates;
            }
        }
    }

    /**
     * 有道接口响应
     */
    @Data
    public static class YoudaoResponse extends ThirdPartyResponseDto {
        /**
         * 有道接口错误码（字符串格式）
         */
        private String youdaoErrorCode;

        /**
         * 结果
         */
        private List<YoudaoResult> Result;

        /**
         * 方向
         */
        private String orientation;

        /**
         * 语言类型
         */
        private String lanType;

        @Data
        public static class YoudaoResult {
            /**
             * 行信息
             */
            private List<YoudaoLine> lines;

            /**
             * 区域信息
             */
            private YoudaoRegion region;
        }

        @Data
        public static class YoudaoLine {
            /**
             * 边界框
             */
            private List<List<Integer>> boundingBox;

            /**
             * 边界框字符串（真实响应格式）
             */
            private String boundingBoxStr;

            /**
             * 文本内容
             */
            private String text;

            /**
             * 置信度
             */
            private Double confidence;

            /**
             * 分割信息（真实响应字段）
             */
            private String segment;

            /**
             * 题目类型（扩展字段）
             */
            private String questionType;

            /**
             * 选项（扩展字段）
             */
            private List<String> options;
        }

        @Data
        public static class YoudaoRegion {
            /**
             * 边界框
             */
            private List<List<Integer>> boundingBox;

            /**
             * 区域类型
             */
            private String type;
        }
    }

    /**
     * 阿里云接口响应
     */
    @Data
    public static class AliyunResponse extends ThirdPartyResponseDto {
        /**
         * 请求ID
         */
        private String RequestId;

        /**
         * 数据
         */
        private AliyunData Data;

        @Data
        public static class AliyunData {
            /**
             * 角度
             */
            private Integer angle;

            /**
             * 高度（算法矫正后）
             */
            private Integer height;

            /**
             * 宽度（算法矫正后）
             */
            private Integer width;

            /**
             * 原图高度
             */
            private Integer orgHeight;

            /**
             * 原图宽度
             */
            private Integer orgWidth;

            /**
             * 页码
             */
            private Integer pageId;

            /**
             * 页标题
             */
            private String pageTitle;

            /**
             * 版面信息
             */
            private List<AliyunDocLayout> docLayout;

            /**
             * 特殊文字信息
             */
            private List<AliyunDocSptext> docSptext;

            /**
             * 图案信息
             */
            private List<AliyunFigure> figure;

            /**
             * 题型大类信息
             */
            private List<AliyunPartInfo> partInfo;

            /**
             * 结构化结果（兼容旧版本）
             */
            private List<AliyunStructuredResult> structuredResult;

            /**
             * Prism版本
             */
            private String prismVersion;

            /**
             * Prism词数
             */
            private Integer prismWnum;

            /**
             * Prism词汇信息
             */
            private List<Object> prismWordsInfo;
        }

        @Data
        public static class AliyunStructuredResult {
            /**
             * 类型
             */
            private String type;

            /**
             * 值
             */
            private String value;

            /**
             * 位置
             */
            private AliyunPosition pos;

            /**
             * 子项
             */
            private List<AliyunStructuredResult> subResults;

            /**
             * 置信度
             */
            private Double confidence;
        }

        @Data
        public static class AliyunPosition {
            /**
             * 顶点坐标
             */
            private List<AliyunPoint> points;

            /**
             * 左上角X
             */
            private Integer x;

            /**
             * 左上角Y
             */
            private Integer y;

            /**
             * 宽度
             */
            private Integer w;

            /**
             * 高度
             */
            private Integer h;
        }

        @Data
        public static class AliyunPoint {
            private Integer x;
            private Integer y;
        }

        /**
         * 版面信息
         */
        @Data
        public static class AliyunDocLayout {
            /**
             * 文字类型（text：普通文字，special_text：特殊文字，table：表格，head：页眉，foot：页脚，side_column：侧栏）
             */
            private String layoutType;

            /**
             * 外矩形四个点的坐标按顺时针排列（左上、右上、右下、左下）
             */
            private List<AliyunPoint> pos;
        }

        /**
         * 特殊文字信息
         */
        @Data
        public static class AliyunDocSptext {
            /**
             * 文字类型（bold：黑体，complex：特殊体）
             */
            private String layoutType;

            /**
             * 外矩形四个点的坐标按顺时针排列（左上、右上、右下、左下）
             */
            private List<AliyunPoint> pos;
        }

        /**
         * 图案信息
         */
        @Data
        public static class AliyunFigure {
            /**
             * 配图类型
             */
            private String type;

            /**
             * 图案左上角横坐标
             */
            private Integer x;

            /**
             * 图案左上角纵坐标
             */
            private Integer y;

            /**
             * 图案宽度
             */
            private Integer w;

            /**
             * 图案高度
             */
            private Integer h;

            /**
             * 图案坐标信息
             */
            private AliyunBox box;

            /**
             * 图案四个点坐标（左上、右上、右下、左下）
             */
            private List<AliyunPoint> points;
        }

        /**
         * 图案坐标信息
         */
        @Data
        public static class AliyunBox {
            private Integer x;
            private Integer y;
            private Integer w;
            private Integer h;
            private Integer angle;
        }

        /**
         * 题型大类信息
         */
        @Data
        public static class AliyunPartInfo {
            /**
             * 题型标题（例如选择题，填空题，解答题）
             */
            private String partTitle;

            /**
             * 对应所有题型的外层大矩形的四个点的坐标数组
             */
            private List<List<AliyunPoint>> posList;

            /**
             * 题目列表
             */
            private List<AliyunSubject> subjectList;
        }

        /**
         * 题目信息
         */
        @Data
        public static class AliyunSubject {
            /**
             * 在part_info中的序号
             */
            private Integer index;

            /**
             * 题目类型
             */
            private Integer type;

            /**
             * 整题文本信息，可能包含latex公式
             */
            private String text;

            /**
             * 置信度
             */
            private Integer prob;

            /**
             * 整题外矩形四个点的坐标按顺时针排列
             */
            private List<List<AliyunPoint>> posList;

            /**
             * 答案位置坐标
             */
            private List<Object> answerList;

            /**
             * 题目元素
             */
            private List<AliyunElement> elementList;

            /**
             * 插图位置信息
             */
            private List<Object> figureList;

            /**
             * 表格位置信息
             */
            private List<Object> tableList;

            /**
             * 选择题答案数量
             */
            private Integer numChoices;
        }

        /**
         * 题目元素信息
         */
        @Data
        public static class AliyunElement {
            /**
             * 题目元素类型（0：题干；1：选项；2：解析；3：答案）
             */
            private Integer type;

            /**
             * 整题文本信息，可能包含latex公式
             */
            private String text;

            /**
             * 外层大矩形的四个点的坐标数组
             */
            private List<List<AliyunPoint>> posList;

            /**
             * 内容数组
             */
            private List<AliyunContent> contentList;
        }

        /**
         * 内容数组信息
         */
        @Data
        public static class AliyunContent {
            /**
             * 内容类型（0：图片；1：文本；2：公式）
             */
            private Integer type;

            /**
             * 置信度
             */
            private Integer prob;

            /**
             * 整题文本信息，可能包含latex公式
             */
            private String string;

            /**
             * 选项
             */
            private String option;

            /**
             * 外层大矩形的四个点的坐标数组
             */
            private List<AliyunPoint> pos;
        }
    }
}
