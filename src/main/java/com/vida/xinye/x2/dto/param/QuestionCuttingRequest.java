package com.vida.xinye.x2.dto.param;

import com.vida.xinye.x2.constant.QuestionCuttingConstant;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 统一的智能切图请求对象
 * 合并原有的多个参数类，简化接口
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
@Data
public class QuestionCuttingRequest {

    // ========== 图片输入方式（三选一） ==========
    
    /**
     * 图片文件（文件上传方式）
     */
    private MultipartFile imageFile;

    /**
     * 图片URL（URL方式）
     */
    private String imageUrl;

    /**
     * 图片Base64编码（Base64方式）
     */
    private String imageBase64;

    // ========== 策略配置 ==========
    
    /**
     * 切图策略
     * 可选值：BEST_QUALITY, FASTEST, AGGREGATED, SPECIFIED, ROUND_ROBIN
     * 默认：BEST_QUALITY
     */
    private String strategy = QuestionCuttingConstant.Default.DEFAULT_STRATEGY;

    /**
     * 指定的接口提供商（当strategy为SPECIFIED时使用）
     * 可选值：tianrang, youdao, aliyun
     */
    private String provider;

    // ========== 业务参数 ==========
    
    /**
     * 学科类型
     * 可选值：math, chinese, english, physics, chemistry, biology, history, geography, politics
     */
    private String subject;

    /**
     * 年级
     * 可选值：primary_1~6, junior_1~3, senior_1~3
     */
    private String grade;

    /**
     * 是否需要详细信息（包含解析、知识点等）
     * 默认：false
     */
    private Boolean needDetail = false;

    /**
     * 是否启用缓存
     * 默认：true
     */
    private Boolean enableCache = true;

    /**
     * 超时时间（秒）
     * 默认：30秒
     */
    private Integer timeoutSeconds = QuestionCuttingConstant.Default.TIMEOUT_SECONDS;

    /**
     * 扩展参数
     * 用于传递特定提供商的额外参数
     */
    private Map<String, Object> extraParams;

    // ========== 便捷构造方法 ==========

    /**
     * 创建文件上传请求
     */
    public static QuestionCuttingRequest fromFile(MultipartFile file) {
        QuestionCuttingRequest request = new QuestionCuttingRequest();
        request.setImageFile(file);
        return request;
    }

    /**
     * 创建URL请求
     */
    public static QuestionCuttingRequest fromUrl(String imageUrl) {
        QuestionCuttingRequest request = new QuestionCuttingRequest();
        request.setImageUrl(imageUrl);
        return request;
    }

    /**
     * 创建Base64请求
     */
    public static QuestionCuttingRequest fromBase64(String imageBase64) {
        QuestionCuttingRequest request = new QuestionCuttingRequest();
        request.setImageBase64(imageBase64);
        return request;
    }

    // ========== 链式配置方法 ==========

    public QuestionCuttingRequest strategy(String strategy) {
        this.strategy = strategy;
        return this;
    }

    public QuestionCuttingRequest provider(String provider) {
        this.provider = provider;
        return this;
    }

    public QuestionCuttingRequest subject(String subject) {
        this.subject = subject;
        return this;
    }

    public QuestionCuttingRequest grade(String grade) {
        this.grade = grade;
        return this;
    }

    public QuestionCuttingRequest needDetail(Boolean needDetail) {
        this.needDetail = needDetail;
        return this;
    }

    public QuestionCuttingRequest enableCache(Boolean enableCache) {
        this.enableCache = enableCache;
        return this;
    }

    public QuestionCuttingRequest timeout(Integer timeoutSeconds) {
        this.timeoutSeconds = timeoutSeconds;
        return this;
    }

    public QuestionCuttingRequest extraParam(String key, Object value) {
        if (this.extraParams == null) {
            this.extraParams = new java.util.HashMap<>();
        }
        this.extraParams.put(key, value);
        return this;
    }

    // ========== 验证方法 ==========

    /**
     * 验证请求参数
     */
    public void validate() {
        if (imageFile == null && imageUrl == null && imageBase64 == null) {
            throw new IllegalArgumentException("必须提供图片文件、URL或Base64编码中的一种");
        }

        if (imageFile != null && (imageUrl != null || imageBase64 != null)) {
            throw new IllegalArgumentException("只能提供一种图片输入方式");
        }

        if (imageUrl != null && imageBase64 != null) {
            throw new IllegalArgumentException("只能提供一种图片输入方式");
        }

        if (timeoutSeconds != null && timeoutSeconds <= 0) {
            throw new IllegalArgumentException("超时时间必须大于0");
        }
    }

    /**
     * 获取图片输入类型
     */
    public String getInputType() {
        if (imageFile != null) return "FILE";
        if (imageUrl != null) return "URL";
        if (imageBase64 != null) return "BASE64";
        return "UNKNOWN";
    }

    /**
     * 是否有图片输入
     */
    public boolean hasImageInput() {
        return imageFile != null || imageUrl != null || imageBase64 != null;
    }
}
