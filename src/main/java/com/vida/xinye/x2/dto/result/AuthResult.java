package com.vida.xinye.x2.dto.result;

import com.vida.xinye.x2.mbg.model.User;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * 统一认证结果对象
 * 包含登录、注册、Token刷新等操作的结果
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
@Data
public class AuthResult {

    // ========== 基础结果信息 ==========
    
    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 操作时间
     */
    private Date timestamp;

    // ========== 认证信息 ==========
    
    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 令牌类型（通常为Bearer）
     */
    private String tokenType = "Bearer";

    /**
     * 访问令牌过期时间（秒）
     */
    private Long expiresIn;

    /**
     * 刷新令牌过期时间（秒）
     */
    private Long refreshExpiresIn;

    /**
     * 权限范围
     */
    private String scope;

    // ========== 用户信息 ==========
    
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户信息
     */
    private User userInfo;

    /**
     * 是否为新用户
     */
    private Boolean isNewUser;

    /**
     * 用户状态
     */
    private String userStatus;

    // ========== 安全信息 ==========
    
    /**
     * 登录方式
     */
    private String loginType;

    /**
     * 登录IP
     */
    private String loginIp;

    /**
     * 登录设备
     */
    private String loginDevice;

    /**
     * 登录时间
     */
    private Date loginTime;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 是否需要二次验证
     */
    private Boolean needSecondAuth;

    /**
     * 二次验证类型
     */
    private String secondAuthType;

    // ========== 业务信息 ==========
    
    /**
     * 用户角色
     */
    private String[] roles;

    /**
     * 用户权限
     */
    private String[] permissions;

    /**
     * 组织信息
     */
    private Map<String, Object> organizationInfo;

    /**
     * 扩展信息
     */
    private Map<String, Object> extraInfo;

    // ========== 静态构造方法 ==========

    /**
     * 创建成功结果
     */
    public static AuthResult success() {
        AuthResult result = new AuthResult();
        result.setSuccess(true);
        result.setTimestamp(new Date());
        return result;
    }

    /**
     * 创建失败结果
     */
    public static AuthResult failure(String errorCode, String errorMessage) {
        AuthResult result = new AuthResult();
        result.setSuccess(false);
        result.setErrorCode(errorCode);
        result.setErrorMessage(errorMessage);
        result.setTimestamp(new Date());
        return result;
    }

    /**
     * 创建登录成功结果
     */
    public static AuthResult loginSuccess(String accessToken, String refreshToken, User user) {
        AuthResult result = success();
        result.setAccessToken(accessToken);
        result.setRefreshToken(refreshToken);
        result.setUserId(user.getId());
        result.setUserInfo(user);
        result.setLoginTime(new Date());
        return result;
    }

    /**
     * 创建Token刷新成功结果
     */
    public static AuthResult refreshSuccess(String accessToken, String refreshToken) {
        AuthResult result = success();
        result.setAccessToken(accessToken);
        result.setRefreshToken(refreshToken);
        return result;
    }

    // ========== 链式配置方法 ==========

    public AuthResult requestId(String requestId) {
        this.requestId = requestId;
        return this;
    }

    public AuthResult tokens(String accessToken, String refreshToken) {
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        return this;
    }

    public AuthResult tokenExpires(Long expiresIn, Long refreshExpiresIn) {
        this.expiresIn = expiresIn;
        this.refreshExpiresIn = refreshExpiresIn;
        return this;
    }

    public AuthResult userInfo(User user) {
        this.userId = user.getId();
        this.userInfo = user;
        return this;
    }

    public AuthResult loginInfo(String loginType, String loginIp, String loginDevice) {
        this.loginType = loginType;
        this.loginIp = loginIp;
        this.loginDevice = loginDevice;
        this.loginTime = new Date();
        return this;
    }

    public AuthResult security(String sessionId, Boolean needSecondAuth, String secondAuthType) {
        this.sessionId = sessionId;
        this.needSecondAuth = needSecondAuth;
        this.secondAuthType = secondAuthType;
        return this;
    }

    public AuthResult permissions(String[] roles, String[] permissions) {
        this.roles = roles;
        this.permissions = permissions;
        return this;
    }

    public AuthResult newUser(Boolean isNewUser) {
        this.isNewUser = isNewUser;
        return this;
    }

    public AuthResult extraInfo(String key, Object value) {
        if (this.extraInfo == null) {
            this.extraInfo = new java.util.HashMap<>();
        }
        this.extraInfo.put(key, value);
        return this;
    }

    // ========== 便捷方法 ==========

    /**
     * 是否登录成功
     */
    public boolean isLoginSuccess() {
        return Boolean.TRUE.equals(success) && accessToken != null && userId != null;
    }

    /**
     * 是否需要验证码
     */
    public boolean needCaptcha() {
        return "NEED_CAPTCHA".equals(errorCode);
    }

    /**
     * 是否账号被锁定
     */
    public boolean isAccountLocked() {
        return "ACCOUNT_LOCKED".equals(errorCode);
    }

    /**
     * 是否密码错误
     */
    public boolean isPasswordError() {
        return "PASSWORD_ERROR".equals(errorCode);
    }

    /**
     * 是否账号不存在
     */
    public boolean isAccountNotFound() {
        return "ACCOUNT_NOT_FOUND".equals(errorCode);
    }

    /**
     * 获取完整的Token信息
     */
    public String getFullToken() {
        if (accessToken == null) {
            return null;
        }
        return tokenType + " " + accessToken;
    }

    /**
     * 获取用户显示名称
     */
    public String getUserDisplayName() {
        if (userInfo == null) {
            return null;
        }
        
        if (userInfo.getNickname() != null && !userInfo.getNickname().trim().isEmpty()) {
            return userInfo.getNickname();
        }
        
        if (userInfo.getUsername() != null && !userInfo.getUsername().trim().isEmpty()) {
            return userInfo.getUsername();
        }
        
        return userInfo.getEmail();
    }

    /**
     * 转换为简化的响应对象（用于API返回）
     */
    public Map<String, Object> toSimpleMap() {
        Map<String, Object> map = new java.util.HashMap<>();
        map.put("success", success);
        
        if (Boolean.TRUE.equals(success)) {
            map.put("accessToken", accessToken);
            map.put("refreshToken", refreshToken);
            map.put("tokenType", tokenType);
            map.put("expiresIn", expiresIn);
            
            if (userInfo != null) {
                Map<String, Object> userMap = new java.util.HashMap<>();
                userMap.put("id", userInfo.getId());
                userMap.put("username", userInfo.getUsername());
                userMap.put("nickname", userInfo.getNickname());
                userMap.put("email", userInfo.getEmail());
                userMap.put("phone", userInfo.getPhone());
                userMap.put("avatar", userInfo.getAvatar());
                map.put("userInfo", userMap);
            }
            
            map.put("isNewUser", isNewUser);
        } else {
            map.put("errorCode", errorCode);
            map.put("errorMessage", errorMessage);
        }
        
        map.put("timestamp", timestamp);
        return map;
    }
}
