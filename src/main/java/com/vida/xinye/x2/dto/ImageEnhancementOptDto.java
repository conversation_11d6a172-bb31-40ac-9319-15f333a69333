package com.vida.xinye.x2.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

/**
 * 文档图像切边增强矫正选项配置类
 * 用于配置图像处理的各种参数
 */
public class ImageEnhancementOptDto {

    /**
     * 增强模式枚举
     */
    public enum EnhanceMode {
        DISABLED(-1, "禁用增强"),
        BRIGHTEN(1, "增亮"),
        ENHANCE_AND_SHARPEN(2, "增强并锐化"),
        BLACK_AND_WHITE(3, "黑白"),
        GRAYSCALE(4, "灰度"),
        SHADOW_REMOVAL(5, "去阴影增强"),
        DITHERED(6, "点阵图");

        private final int value;
        private final String description;

        EnhanceMode(int value, String description) {
            this.value = value;
            this.description = description;
        }

        public int getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public static EnhanceMode fromValue(int value) {
            for (EnhanceMode mode : values()) {
                if (mode.value == value) {
                    return mode;
                }
            }
            throw new IllegalArgumentException("Invalid enhance mode value: " + value);
        }
    }

    /**
     * 是否执行操作枚举
     */
    public enum OperationFlag {
        DISABLED(0, "不执行"),
        ENABLED(1, "执行");

        private final int value;
        private final String description;

        OperationFlag(int value, String description) {
            this.value = value;
            this.description = description;
        }

        public int getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public static OperationFlag fromValue(int value) {
            for (OperationFlag flag : values()) {
                if (flag.value == value) {
                    return flag;
                }
            }
            throw new IllegalArgumentException("Invalid operation flag value: " + value);
        }
    }

    /**
     * 位置信息类
     */
    public static class SizeAndPosition {
        private int width;
        private int height;
        private Point topLeft;
        private Point topRight;
        private Point bottomRight;
        private Point bottomLeft;

        public SizeAndPosition() {
        }

        public SizeAndPosition(int width, int height, Point topLeft, Point topRight, Point bottomRight,
                               Point bottomLeft) {
            this.width = width;
            this.height = height;
            this.topLeft = topLeft;
            this.topRight = topRight;
            this.bottomRight = bottomRight;
            this.bottomLeft = bottomLeft;
        }

        // Getters and Setters
        public int getWidth() {
            return width;
        }

        public void setWidth(int width) {
            this.width = width;
        }

        public int getHeight() {
            return height;
        }

        public void setHeight(int height) {
            this.height = height;
        }

        public Point getTopLeft() {
            return topLeft;
        }

        public void setTopLeft(Point topLeft) {
            this.topLeft = topLeft;
        }

        public Point getTopRight() {
            return topRight;
        }

        public void setTopRight(Point topRight) {
            this.topRight = topRight;
        }

        public Point getBottomRight() {
            return bottomRight;
        }

        public void setBottomRight(Point bottomRight) {
            this.bottomRight = bottomRight;
        }

        public Point getBottomLeft() {
            return bottomLeft;
        }

        public void setBottomLeft(Point bottomLeft) {
            this.bottomLeft = bottomLeft;
        }


        /**
         * 转换为API格式的字符串
         * 格式: width,height,x1,y1,x2,y2,x3,y3,x4,y4
         */
        public String toApiString() {
            return String.format("%d,%d,%d,%d,%d,%d,%d,%d,%d,%d",
                    width, height,
                    topLeft.getX(), topLeft.getY(),
                    topRight.getX(), topRight.getY(),
                    bottomRight.getX(), bottomRight.getY(),
                    bottomLeft.getX(), bottomLeft.getY());
        }

        /**
         * 从API格式字符串解析
         */
        public static SizeAndPosition fromApiString(String apiString) {
            String[] parts = apiString.split(",");
            if (parts.length != 10) {
                throw new IllegalArgumentException("Invalid size and position format: " + apiString);
            }

            return new SizeAndPosition(
                    Integer.parseInt(parts[0]), // width
                    Integer.parseInt(parts[1]), // height
                    new Point(Integer.parseInt(parts[2]), Integer.parseInt(parts[3])), // topLeft
                    new Point(Integer.parseInt(parts[4]), Integer.parseInt(parts[5])), // topRight
                    new Point(Integer.parseInt(parts[6]), Integer.parseInt(parts[7])), // bottomRight
                    new Point(Integer.parseInt(parts[8]), Integer.parseInt(parts[9])) // bottomLeft
            );
        }


        @Override
        public String toString() {
            return "SizeAndPosition{" +
                    "width=" + width +
                    ", height=" + height +
                    ", topLeft=" + topLeft +
                    ", topRight=" + topRight +
                    ", bottomRight=" + bottomRight +
                    ", bottomLeft=" + bottomLeft +
                    '}';
        }
    }

    /**
     * 坐标点类
     */
    public static class Point {
        private int x;
        private int y;

        public Point() {
        }

        public Point(int x, int y) {
            this.x = x;
            this.y = y;
        }

        public int getX() {
            return x;
        }

        public void setX(int x) {
            this.x = x;
        }

        public int getY() {
            return y;
        }

        public void setY(int y) {
            this.y = y;
        }

        @Override
        public String toString() {
            return "(" + x + ", " + y + ")";
        }
    }

    // 主要属性
    @JsonProperty("enhance_mode")
    private EnhanceMode enhanceMode = EnhanceMode.DISABLED; // 默认为-1

    @JsonProperty("crop_image")
    private OperationFlag cropImage = OperationFlag.ENABLED; // 默认为1

    @JsonProperty("only_position")
    private OperationFlag onlyPosition = OperationFlag.DISABLED; // 默认为0

    @JsonProperty("dewarp_image")
    private OperationFlag dewarpImage = OperationFlag.ENABLED; // 默认为1

    @JsonProperty("deblur_image")
    private OperationFlag deblurImage = OperationFlag.DISABLED; // 默认为0

    @JsonProperty("correct_direction")
    private OperationFlag correctDirection = OperationFlag.DISABLED; // 默认为0

    @JsonProperty("round_image")
    private OperationFlag roundImage = OperationFlag.DISABLED; // 默认为0

    @JsonProperty("jpeg_quality")
    private Integer jpegQuality = 95; // 默认为95

    @JsonProperty("size_and_position")
    private SizeAndPosition sizeAndPosition;

    // 构造函数
    public ImageEnhancementOptDto() {
    }

    // Getters and Setters
    public EnhanceMode getEnhanceMode() {
        return enhanceMode;
    }

    public void setEnhanceMode(EnhanceMode enhanceMode) {
        this.enhanceMode = enhanceMode;
    }

    public void setEnhanceMode(int value) {
        this.enhanceMode = EnhanceMode.fromValue(value);
    }

    public OperationFlag getCropImage() {
        return cropImage;
    }

    public void setCropImage(OperationFlag cropImage) {
        this.cropImage = cropImage;
    }

    public void setCropImage(int value) {
        this.cropImage = OperationFlag.fromValue(value);
    }

    public OperationFlag getOnlyPosition() {
        return onlyPosition;
    }

    public void setOnlyPosition(OperationFlag onlyPosition) {
        this.onlyPosition = onlyPosition;
    }

    public void setOnlyPosition(int value) {
        this.onlyPosition = OperationFlag.fromValue(value);
    }

    public OperationFlag getDewarpImage() {
        return dewarpImage;
    }

    public void setDewarpImage(OperationFlag dewarpImage) {
        this.dewarpImage = dewarpImage;
    }

    public void setDewarpImage(int value) {
        this.dewarpImage = OperationFlag.fromValue(value);
    }

    public OperationFlag getDeblurImage() {
        return deblurImage;
    }

    public void setDeblurImage(OperationFlag deblurImage) {
        this.deblurImage = deblurImage;
    }

    public void setDeblurImage(int value) {
        this.deblurImage = OperationFlag.fromValue(value);
    }

    public OperationFlag getCorrectDirection() {
        return correctDirection;
    }

    public void setCorrectDirection(OperationFlag correctDirection) {
        this.correctDirection = correctDirection;
    }

    public void setCorrectDirection(int value) {
        this.correctDirection = OperationFlag.fromValue(value);
    }

    public OperationFlag getRoundImage() {
        return roundImage;
    }

    public void setRoundImage(OperationFlag roundImage) {
        this.roundImage = roundImage;
    }

    public void setRoundImage(int value) {
        this.roundImage = OperationFlag.fromValue(value);
    }

    public Integer getJpegQuality() {
        return jpegQuality;
    }

    public void setJpegQuality(Integer jpegQuality) {
        if (jpegQuality != null && (jpegQuality < 1 || jpegQuality > 100)) {
            throw new IllegalArgumentException("JPEG quality must be between 1 and 100");
        }
        this.jpegQuality = jpegQuality;
    }

    public SizeAndPosition getSizeAndPosition() {
        return sizeAndPosition;
    }

    public void setSizeAndPosition(SizeAndPosition sizeAndPosition) {
        this.sizeAndPosition = sizeAndPosition;
    }

    /**
     * 转换为HashMap，用于API调用
     */
    public java.util.HashMap<String, Object> toHashMap() {
        java.util.HashMap<String, Object> map = new java.util.HashMap<>();

        if (enhanceMode != null) {
            map.put("enhance_mode", enhanceMode.getValue());
        }
        if (cropImage != null) {
            map.put("crop_image", cropImage.getValue());
        }
        if (onlyPosition != null) {
            map.put("only_position", onlyPosition.getValue());
        }
        if (dewarpImage != null) {
            map.put("dewarp_image", dewarpImage.getValue());
        }
        if (deblurImage != null) {
            map.put("deblur_image", deblurImage.getValue());
        }
        if (correctDirection != null) {
            map.put("correct_direction", correctDirection.getValue());
        }
        if (roundImage != null) {
            map.put("round_image", roundImage.getValue());
        }
        if (jpegQuality != null) {
            map.put("jpeg_quality", jpegQuality);
        }
        if (sizeAndPosition != null) {
            map.put("size_and_position", sizeAndPosition.toApiString());
        }

        return map;
    }

    /**
     * 构建器模式
     */
    public static class Builder {
        private ImageEnhancementOptDto options = new ImageEnhancementOptDto();

        public Builder enhanceMode(EnhanceMode mode) {
            options.setEnhanceMode(mode);
            return this;
        }

        public Builder enhanceMode(int value) {
            options.setEnhanceMode(value);
            return this;
        }

        public Builder cropImage(OperationFlag flag) {
            options.setCropImage(flag);
            return this;
        }

        public Builder cropImage(int value) {
            options.setCropImage(value);
            return this;
        }

        public Builder onlyPosition(OperationFlag flag) {
            options.setOnlyPosition(flag);
            return this;
        }

        public Builder onlyPosition(int value) {
            options.setOnlyPosition(value);
            return this;
        }

        public Builder dewarpImage(OperationFlag flag) {
            options.setDewarpImage(flag);
            return this;
        }

        public Builder dewarpImage(int value) {
            options.setDewarpImage(value);
            return this;
        }

        public Builder deblurImage(OperationFlag flag) {
            options.setDeblurImage(flag);
            return this;
        }

        public Builder deblurImage(int value) {
            options.setDeblurImage(value);
            return this;
        }

        public Builder correctDirection(OperationFlag flag) {
            options.setCorrectDirection(flag);
            return this;
        }

        public Builder correctDirection(int value) {
            options.setCorrectDirection(value);
            return this;
        }

        public Builder roundImage(OperationFlag flag) {
            options.setRoundImage(flag);
            return this;
        }

        public Builder roundImage(int value) {
            options.setRoundImage(value);
            return this;
        }

        public Builder jpegQuality(Integer quality) {
            options.setJpegQuality(quality);
            return this;
        }

        public Builder sizeAndPosition(SizeAndPosition sizeAndPosition) {
            options.setSizeAndPosition(sizeAndPosition);
            return this;
        }

        public ImageEnhancementOptDto build() {
            return options;
        }
    }

    public static Builder builder() {
        return new Builder();
    }

    /**
     * 返回一份预设的默认配置
     * 仅传图片时可直接使用该默认配置
     */
    public static ImageEnhancementOptDto defaults() {
        return ImageEnhancementOptDto.builder()
                .enhanceMode(EnhanceMode.DISABLED)
                .cropImage(OperationFlag.DISABLED)
                .onlyPosition(OperationFlag.DISABLED)
                .dewarpImage(OperationFlag.ENABLED)
                .deblurImage(OperationFlag.DISABLED)
                .correctDirection(OperationFlag.DISABLED)
                .roundImage(OperationFlag.DISABLED)
                .jpegQuality(95)
                .build();
    }

    @Override
    public String toString() {
        return "ImageEnhancementOptions{" +
                "enhanceMode=" + enhanceMode +
                ", cropImage=" + cropImage +
                ", onlyPosition=" + onlyPosition +
                ", dewarpImage=" + dewarpImage +
                ", deblurImage=" + deblurImage +
                ", correctDirection=" + correctDirection +
                ", roundImage=" + roundImage +
                ", jpegQuality=" + jpegQuality +
                ", sizeAndPosition=" + sizeAndPosition +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        ImageEnhancementOptDto that = (ImageEnhancementOptDto) o;
        return Objects.equals(enhanceMode, that.enhanceMode) &&
                Objects.equals(cropImage, that.cropImage) &&
                Objects.equals(onlyPosition, that.onlyPosition) &&
                Objects.equals(dewarpImage, that.dewarpImage) &&
                Objects.equals(deblurImage, that.deblurImage) &&
                Objects.equals(correctDirection, that.correctDirection) &&
                Objects.equals(roundImage, that.roundImage) &&
                Objects.equals(jpegQuality, that.jpegQuality) &&
                Objects.equals(sizeAndPosition, that.sizeAndPosition);
    }

    @Override
    public int hashCode() {
        return Objects.hash(enhanceMode, cropImage, onlyPosition, dewarpImage,
                deblurImage, correctDirection, roundImage, jpegQuality, sizeAndPosition);
    }
}
