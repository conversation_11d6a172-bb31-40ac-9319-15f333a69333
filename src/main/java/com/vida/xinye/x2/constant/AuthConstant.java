package com.vida.xinye.x2.constant;

/**
 * 统一认证常量定义
 * 整合原有的分散常量定义
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
public interface AuthConstant {

    // ========== 原有常量（保持兼容） ==========

    /**
     * 认证信息Http请求头
     */
    String JWT_TOKEN_HEADER = "Authorization";

    /**
     * JWT令牌前缀
     */
    String JWT_TOKEN_PREFIX = "Bearer ";

    /**
     * 认证信息Http请求头
     */
    String USER_HEADER = "X-User";

    /**
     * 角色命名分隔符 [ID]@[NAME]
     */
    String ROLENAME_SPLIT = "@";

    // ========== 新增常量 ==========

    /**
     * 登录方式常量
     */
    interface LoginType {
        String EMAIL_PASSWORD = "byemail_password";
        String EMAIL_CAPTCHA = "byemail";
        String MOBILE_PASSWORD = "bymobile_password";
        String MOBILE_CAPTCHA = "bysms";
        String WECHAT = "byweixin";
        String QQ = "byqq";
        String WEIBO = "bysina";
        String APPLE_ID = "byappleid";
        String UMENG_ONEKEY = "byyumeng";
    }

    /**
     * 错误码常量
     */
    interface ErrorCode {
        String SUCCESS = "SUCCESS";
        String INVALID_PARAMETER = "INVALID_PARAMETER";
        String ACCOUNT_NOT_FOUND = "ACCOUNT_NOT_FOUND";
        String PASSWORD_ERROR = "PASSWORD_ERROR";
        String CAPTCHA_ERROR = "CAPTCHA_ERROR";
        String NEED_CAPTCHA = "NEED_CAPTCHA";
        String ACCOUNT_LOCKED = "ACCOUNT_LOCKED";
        String IP_BLOCKED = "IP_BLOCKED";
        String TOO_MANY_FAILURES = "TOO_MANY_FAILURES";
        String INVALID_TOKEN = "INVALID_TOKEN";
        String TOKEN_EXPIRED = "TOKEN_EXPIRED";
        String INVALID_REFRESH_TOKEN = "INVALID_REFRESH_TOKEN";
        String USER_NOT_FOUND = "USER_NOT_FOUND";
        String UNSUPPORTED_LOGIN_TYPE = "UNSUPPORTED_LOGIN_TYPE";
        String INTERNAL_ERROR = "INTERNAL_ERROR";
    }

    /**
     * 验证码类型常量
     */
    interface CaptchaType {
        String LOGIN = "login";
        String REGISTER = "register";
        String RESET_PASSWORD = "reset_password";
        String IMAGE = "image";
    }

    /**
     * 用户状态常量
     */
    interface UserStatus {
        String NORMAL = "normal";
        String DISABLED = "disabled";
        String LOCKED = "locked";
        String PENDING = "pending";
        String DELETED = "deleted";
    }

    /**
     * 设备类型常量
     */
    interface DeviceType {
        String WEB = "web";
        String IOS = "ios";
        String ANDROID = "android";
        String MINIPROGRAM = "miniprogram";
        String UNKNOWN = "unknown";
    }

    /**
     * 缓存键前缀常量
     */
    interface CacheKey {
        String USER_PREFIX = "auth:user:";
        String TOKEN_PREFIX = "auth:token:";
        String CAPTCHA_PREFIX = "auth:captcha:";
        String LOGIN_FAILURE_PREFIX = "auth:login_failure:";
        String ACCOUNT_LOCK_PREFIX = "auth:account_lock:";
    }

    /**
     * 默认值常量
     */
    interface Default {
        long TOKEN_EXPIRATION = 3600L; // 1小时
        long REFRESH_TOKEN_EXPIRATION = 7200L; // 2小时
        int CAPTCHA_EXPIRATION = 300; // 5分钟
        int MAX_LOGIN_ATTEMPTS = 5;
        int ACCOUNT_LOCK_MINUTES = 30;
        int CAPTCHA_LENGTH = 6;
        int PASSWORD_MIN_LENGTH = 6;
        int PASSWORD_MAX_LENGTH = 20;
    }

    /**
     * 正则表达式常量
     */
    interface Regex {
        String EMAIL = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        String PHONE = "^1[3-9]\\d{9}$";
        String USERNAME = "^[a-zA-Z0-9_]{3,20}$";
        String CAPTCHA = "^\\d{4,6}$";
    }
}
