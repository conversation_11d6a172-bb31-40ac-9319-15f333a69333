package com.vida.xinye.x2.exception;

import com.vida.xinye.x2.api.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 全局异常处理
 * Created by wangpf on 2022/8/22.
 */
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ResponseBody
    @ExceptionHandler(value = ApiException.class)
    public CommonResult handleApiException(ApiException e) {
        log.error("API exception: ", e);
        if (e.getErrorCode() != null) {
            return CommonResult.failed(e.getErrorCode(), e.getMessage());
        }
        return CommonResult.failed(e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(value = RateLimitException.class)
    public CommonResult handleRateLimitException(RateLimitException e) {
        log.warn("Rate limit exception: {}", e.getMessage());
        return CommonResult.failed(e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(value = BlacklistException.class)
    public CommonResult handleBlacklistException(BlacklistException e) {
        log.warn("Blacklist exception: {}", e.getMessage());
        return CommonResult.forbidden(null);
    }

    @ResponseBody
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public CommonResult handleValidException(MethodArgumentNotValidException e) {
        log.error("Validation exception: ", e);
        BindingResult bindingResult = e.getBindingResult();
        String message = null;
        if (bindingResult.hasErrors()) {
            FieldError fieldError = bindingResult.getFieldError();
            if (fieldError != null) {
                message = fieldError.getDefaultMessage();
            }
        }
        return CommonResult.validateFailed(message);
    }

    @ResponseBody
    @ExceptionHandler(value = MissingServletRequestParameterException.class)
    public CommonResult handleMissingServletRequestParameterException(MissingServletRequestParameterException e) {
        log.error("Missing request parameter exception: ", e);
        String message = String.format("缺少必需的请求参数: %s", e.getParameterName());
        return CommonResult.validateFailed(message);
    }

    @ResponseBody
    @ExceptionHandler(value = BadSqlGrammarException.class)
    public CommonResult handleSqlGrammarException(BadSqlGrammarException e) {
        log.error("Database SQL exception: ", e);
        return CommonResult.failed("服务器内部错误！");
    }

    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public CommonResult handleException(Exception e) {
        log.error("Unexpected exception: ", e);
        return CommonResult.failed("请求失败！");
    }

}
