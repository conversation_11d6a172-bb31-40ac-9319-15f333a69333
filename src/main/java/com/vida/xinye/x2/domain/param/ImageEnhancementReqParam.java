package com.vida.xinye.x2.domain.param;

import com.vida.xinye.x2.dto.ImageEnhancementOptDto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Author：Chenjy
 * Date:2025/8/18
 * Description:
 */
public class ImageEnhancementReqParam {
    @NotBlank(message = "图片数据不能为空")
    private String imageBase64;

    private ImageEnhancementOptDto options;

    public ImageEnhancementReqParam() {}

    public ImageEnhancementReqParam(String imageBase64, ImageEnhancementOptDto options) {
        this.imageBase64 = imageBase64;
        this.options = options;
    }

    // Getters and Setters
    public String getImageBase64() {
        return imageBase64;
    }

    public void setImageBase64(String imageBase64) {
        this.imageBase64 = imageBase64;
    }

    public ImageEnhancementOptDto getOptions() {
        return options;
    }

    public void setOptions(ImageEnhancementOptDto options) {
        this.options = options;
    }
}
