# 测试环境配置
spring:
  
  # 数据库配置 - 使用H2内存数据库进行测试
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console
      
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        
  # Redis配置 - 使用嵌入式Redis进行测试
  redis:
    host: localhost
    port: 6379
    database: 15  # 使用独立的测试数据库
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        
  # 邮件配置 - 测试环境使用Mock
  mail:
    host: smtp.example.com
    port: 587
    username: <EMAIL>
    password: testpassword
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            
# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.vida.xinye.x2.mbg.model
  configuration:
    map-underscore-to-camel-case: true
    
# JWT配置
jwt:
  secret: test-secret-key-for-integration-testing-only
  expiration: 3600000  # 1小时
  refresh-expiration: 604800000  # 7天
  
# 登录安全配置
login:
  security:
    max-failure-count: 5
    lock-duration-minutes: 30
    captcha-expiration-seconds: 300
    
# 验证码配置
captcha:
  sms:
    enabled: false  # 测试环境禁用真实短信发送
  email:
    enabled: false  # 测试环境禁用真实邮件发送
    
# 第三方登录配置
third-party:
  wechat:
    app-id: test-app-id
    app-secret: test-app-secret
    enabled: false  # 测试环境禁用真实第三方登录
  qq:
    app-id: test-qq-app-id
    app-secret: test-qq-app-secret
    enabled: false
    
# 日志配置
logging:
  level:
    com.vida.xinye.x2: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    org.mybatis: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    
# 测试专用配置
test:
  # 是否启用真实的外部服务调用
  enable-real-services: false
  
  # 测试数据配置
  test-data:
    create-test-users: true
    cleanup-after-test: true
    
  # Mock配置
  mock:
    sms-service: true
    email-service: true
    third-party-service: true
    
# 性能测试配置
performance:
  # 并发测试配置
  concurrent:
    thread-count: 10
    request-count: 100
    
  # 响应时间阈值（毫秒）
  response-time:
    login: 1000
    captcha: 500
    
# 安全测试配置
security:
  test:
    # 是否启用安全测试
    enabled: true
    
    # SQL注入测试
    sql-injection:
      enabled: true
      
    # XSS测试
    xss:
      enabled: true
      
    # 频率限制测试
    rate-limit:
      enabled: true

# 智能切题测试配置
question:
  cutting:
    # 天壤接口配置 - 测试环境
    tianrang:
      url: https://questiontr.market.alicloudapi.com/sjtmjc
      api-key: test_tianrang_key
      timeout: 30000
      enabled: false  # 测试环境禁用真实API调用
      retry-count: 1

    # 有道接口配置 - 测试环境
    youdao:
      url: https://openapi.youdao.com/cut_question
      app-key: test_youdao_key
      app-secret: test_youdao_secret
      timeout: 30000
      enabled: false  # 测试环境禁用真实API调用
      retry-count: 1

    # 阿里云接口配置 - 测试环境
    aliyun:
      endpoint: ocr-api.cn-hangzhou.aliyuncs.com
      access-key-id: test_aliyun_access_key_id
      access-key-secret: test_aliyun_access_key_secret
      region-id: cn-hangzhou
      product: ocr-api
      version: 2021-07-07
      action: RecognizeEduPaperStructed
      need-rotate: false
      output-oricoord: false
      timeout: 30000
      enabled: false  # 测试环境禁用真实API调用
      retry-count: 1

    # 通用配置
    common:
      default-strategy: BEST_QUALITY
      default-provider: tianrang
      enable-aggregation: false
      max-concurrent-requests: 5
      max-image-size: 10485760  # 10MB
      supported-formats: [jpg, jpeg, png, bmp, gif]
      enable-cache: false  # 测试环境禁用缓存
      cache-expire-seconds: 3600
