package com.vida.xinye.x2.adapter;

import com.vida.xinye.x2.adapter.impl.AliyunAdapter;
import com.vida.xinye.x2.config.QuestionCuttingConfig;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyRequestDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyResponseDto;
import com.vida.xinye.x2.enums.QuestionCuttingEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 阿里云适配器测试
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
@ExtendWith(MockitoExtension.class)
class AliyunAdapterTest {

    @Mock
    private QuestionCuttingConfig config;

    @InjectMocks
    private AliyunAdapter aliyunAdapter;

    private QuestionCuttingConfig.AliyunConfig aliyunConfig;

    @BeforeEach
    void setUp() {
        aliyunConfig = new QuestionCuttingConfig.AliyunConfig();
        aliyunConfig.setEnabled(true);
        aliyunConfig.setAccessKeyId("test_access_key_id");
        aliyunConfig.setAccessKeySecret("test_access_key_secret");
        aliyunConfig.setEndpoint("ocr-api.cn-hangzhou.aliyuncs.com");
        aliyunConfig.setTimeout(30000);
        aliyunConfig.setRetryCount(2);
        aliyunConfig.setNeedRotate(false);
        aliyunConfig.setOutputOricoord(false);

        when(config.getAliyun()).thenReturn(aliyunConfig);
    }

    @Test
    void testGetProvider() {
        assertEquals(QuestionCuttingEnum.Provider.ALIYUN, aliyunAdapter.getProvider());
    }

    @Test
    void testIsAvailable_WhenConfigValid() {
        assertTrue(aliyunAdapter.isAvailable());
    }

    @Test
    void testIsAvailable_WhenConfigDisabled() {
        aliyunConfig.setEnabled(false);
        assertFalse(aliyunAdapter.isAvailable());
    }

    @Test
    void testIsAvailable_WhenAccessKeyIdMissing() {
        aliyunConfig.setAccessKeyId("");
        assertFalse(aliyunAdapter.isAvailable());
    }

    @Test
    void testIsAvailable_WhenAccessKeySecretMissing() {
        aliyunConfig.setAccessKeySecret("");
        assertFalse(aliyunAdapter.isAvailable());
    }

    @Test
    void testIsAvailable_WhenEndpointMissing() {
        aliyunConfig.setEndpoint("");
        assertFalse(aliyunAdapter.isAvailable());
    }

    @Test
    void testGetPriority() {
        assertEquals(30, aliyunAdapter.getPriority());
    }

    @Test
    void testGetQualityScore() {
        assertEquals(4, aliyunAdapter.getQualityScore());
    }

    @Test
    void testGetAverageResponseTime() {
        assertEquals(5000, aliyunAdapter.getAverageResponseTime());
    }

    @Test
    void testConvertToStandardResult_Success() {
        // 准备测试数据
        ThirdPartyResponseDto.AliyunResponse aliyunResponse = createMockAliyunResponse();
        aliyunResponse.setSuccess(true);
        aliyunResponse.setResponseTime(2000L);

        // 执行测试
        QuestionCuttingResultDto result = aliyunAdapter.convertToStandardResult(aliyunResponse, "test_request_id");

        // 验证结果
        assertNotNull(result);
        assertEquals("test_request_id", result.getRequestId());
        assertEquals("aliyun", result.getProvider());
        assertTrue(result.getSuccess());
        assertEquals(2000L, result.getProcessingTime());
        assertNotNull(result.getCreateTime());
    }

    @Test
    void testConvertToStandardResult_Failure() {
        // 准备测试数据
        ThirdPartyResponseDto.AliyunResponse aliyunResponse = new ThirdPartyResponseDto.AliyunResponse();
        aliyunResponse.setSuccess(false);
        aliyunResponse.setErrorMessage("API调用失败");

        // 执行测试
        QuestionCuttingResultDto result = aliyunAdapter.convertToStandardResult(aliyunResponse, "test_request_id");

        // 验证结果
        assertNotNull(result);
        assertEquals("test_request_id", result.getRequestId());
        assertEquals("aliyun", result.getProvider());
        assertFalse(result.getSuccess());
        assertEquals("API调用失败", result.getErrorMessage());
    }

    @Test
    void testConvertToStandardResult_WithPartInfo() {
        // 准备测试数据
        ThirdPartyResponseDto.AliyunResponse aliyunResponse = createMockAliyunResponseWithPartInfo();
        aliyunResponse.setSuccess(true);
        aliyunResponse.setResponseTime(2000L);

        // 执行测试
        QuestionCuttingResultDto result = aliyunAdapter.convertToStandardResult(aliyunResponse, "test_request_id");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertNotNull(result.getQuestions());
        assertFalse(result.getQuestions().isEmpty());

        QuestionCuttingResultDto.QuestionInfo question = result.getQuestions().get(0);
        assertNotNull(question);
        assertEquals("aliyun_1", question.getQuestionId());
        assertEquals(1, question.getQuestionNumber());
        assertNotNull(question.getContent());

        // 验证扩展属性中的category
        assertNotNull(question.getExtraProperties());
        assertEquals("选择题", question.getExtraProperties().get("category"));
    }

    /**
     * 创建模拟的阿里云响应
     */
    private ThirdPartyResponseDto.AliyunResponse createMockAliyunResponse() {
        ThirdPartyResponseDto.AliyunResponse response = new ThirdPartyResponseDto.AliyunResponse();
        response.setRequestId("test_request_id");
        
        ThirdPartyResponseDto.AliyunResponse.AliyunData data = 
            new ThirdPartyResponseDto.AliyunResponse.AliyunData();
        data.setWidth(1920);
        data.setHeight(1080);
        data.setOrgWidth(1920);
        data.setOrgHeight(1080);
        
        response.setData(data);
        return response;
    }

    /**
     * 创建包含part_info的模拟阿里云响应
     */
    private ThirdPartyResponseDto.AliyunResponse createMockAliyunResponseWithPartInfo() {
        ThirdPartyResponseDto.AliyunResponse response = createMockAliyunResponse();
        
        // 创建part_info
        List<ThirdPartyResponseDto.AliyunResponse.AliyunPartInfo> partInfoList = new ArrayList<>();
        ThirdPartyResponseDto.AliyunResponse.AliyunPartInfo partInfo = 
            new ThirdPartyResponseDto.AliyunResponse.AliyunPartInfo();
        partInfo.setPartTitle("选择题");
        
        // 创建subject_list
        List<ThirdPartyResponseDto.AliyunResponse.AliyunSubject> subjectList = new ArrayList<>();
        ThirdPartyResponseDto.AliyunResponse.AliyunSubject subject = 
            new ThirdPartyResponseDto.AliyunResponse.AliyunSubject();
        subject.setIndex(0);
        subject.setType(0); // 选择题
        subject.setText("1. 下列哪个选项是正确的？");
        subject.setProb(95);
        
        subjectList.add(subject);
        partInfo.setSubjectList(subjectList);
        partInfoList.add(partInfo);
        
        response.getData().setPartInfo(partInfoList);
        return response;
    }
}
