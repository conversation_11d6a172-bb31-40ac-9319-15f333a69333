package com.vida.xinye.x2.adapter;

import com.vida.xinye.x2.adapter.impl.AliyunAdapter;
import com.vida.xinye.x2.config.QuestionCuttingConfig;
import com.vida.xinye.x2.enums.QuestionCuttingEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 阿里云适配器简单测试
 * 只测试基本功能，避免复杂的API调用
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
@ExtendWith(MockitoExtension.class)
class AliyunAdapterSimpleTest {

    @Mock
    private QuestionCuttingConfig config;

    @InjectMocks
    private AliyunAdapter aliyunAdapter;

    private QuestionCuttingConfig.AliyunConfig aliyunConfig;

    @BeforeEach
    void setUp() {
        aliyunConfig = new QuestionCuttingConfig.AliyunConfig();
        aliyunConfig.setEnabled(true);
        aliyunConfig.setAccessKeyId("test_access_key_id");
        aliyunConfig.setAccessKeySecret("test_access_key_secret");
        aliyunConfig.setEndpoint("ocr-api.cn-hangzhou.aliyuncs.com");
        aliyunConfig.setTimeout(30000);
        aliyunConfig.setRetryCount(2);
        aliyunConfig.setNeedRotate(false);
        aliyunConfig.setOutputOricoord(false);
        aliyunConfig.setRegionId("cn-hangzhou");
        aliyunConfig.setProduct("ocr-api");
        aliyunConfig.setVersion("2021-07-07");
        aliyunConfig.setAction("RecognizeEduPaperStructed");

        when(config.getAliyun()).thenReturn(aliyunConfig);
    }

    @Test
    void testGetProvider() {
        assertEquals(QuestionCuttingEnum.Provider.ALIYUN, aliyunAdapter.getProvider());
    }

    @Test
    void testIsAvailable_WhenConfigValid() {
        assertTrue(aliyunAdapter.isAvailable());
    }

    @Test
    void testIsAvailable_WhenConfigDisabled() {
        aliyunConfig.setEnabled(false);
        assertFalse(aliyunAdapter.isAvailable());
    }

    @Test
    void testIsAvailable_WhenAccessKeyIdMissing() {
        aliyunConfig.setAccessKeyId("");
        assertFalse(aliyunAdapter.isAvailable());
    }

    @Test
    void testIsAvailable_WhenAccessKeySecretMissing() {
        aliyunConfig.setAccessKeySecret("");
        assertFalse(aliyunAdapter.isAvailable());
    }

    @Test
    void testIsAvailable_WhenEndpointMissing() {
        aliyunConfig.setEndpoint("");
        assertFalse(aliyunAdapter.isAvailable());
    }

    @Test
    void testGetPriority() {
        assertEquals(30, aliyunAdapter.getPriority());
    }

    @Test
    void testGetQualityScore() {
        assertEquals(4, aliyunAdapter.getQualityScore());
    }

    @Test
    void testGetAverageResponseTime() {
        assertEquals(5000, aliyunAdapter.getAverageResponseTime());
    }

    @Test
    void testConfigurationProperties() {
        // 验证新增的配置属性
        assertEquals("cn-hangzhou", aliyunConfig.getRegionId());
        assertEquals("ocr-api", aliyunConfig.getProduct());
        assertEquals("2021-07-07", aliyunConfig.getVersion());
        assertEquals("RecognizeEduPaperStructed", aliyunConfig.getAction());
        assertEquals(false, aliyunConfig.getNeedRotate());
        assertEquals(false, aliyunConfig.getOutputOricoord());
    }

    @Test
    void testConfigurationDefaults() {
        // 测试默认值
        QuestionCuttingConfig.AliyunConfig defaultConfig = new QuestionCuttingConfig.AliyunConfig();
        assertEquals("cn-hangzhou", defaultConfig.getRegionId());
        assertEquals("ocr-api", defaultConfig.getProduct());
        assertEquals("2021-07-07", defaultConfig.getVersion());
        assertEquals("RecognizeEduPaperStructed", defaultConfig.getAction());
        assertEquals(false, defaultConfig.getNeedRotate());
        assertEquals(false, defaultConfig.getOutputOricoord());
    }

    @Test
    void testAdapterBasicProperties() {
        // 验证适配器基本属性
        assertNotNull(aliyunAdapter.getProvider());
        assertTrue(aliyunAdapter.getPriority() > 0);
        assertTrue(aliyunAdapter.getQualityScore() > 0);
        assertTrue(aliyunAdapter.getAverageResponseTime() > 0);
    }

    @Test
    void testAdapterComparison() {
        // 验证阿里云适配器在多提供商中的定位
        // 优先级：天壤(10) < 有道(20) < 阿里云(30)
        assertEquals(30, aliyunAdapter.getPriority());
        
        // 质量评分：有道(3) < 阿里云(4) < 天壤(5)
        assertEquals(4, aliyunAdapter.getQualityScore());
        
        // 响应时间：阿里云(5000ms)
        assertEquals(5000, aliyunAdapter.getAverageResponseTime());
    }
}
