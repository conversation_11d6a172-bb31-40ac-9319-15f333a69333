package com.vida.xinye.x2.service;

import com.vida.xinye.x2.dto.param.LoginRequest;
import com.vida.xinye.x2.dto.result.AuthResult;
import com.vida.xinye.x2.constant.AuthConstant;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Auth Service Test
 * Test unified authentication service core functions
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
@SpringBootTest
@ActiveProfiles("test")
public class AuthServiceTest {

    // 注意：这里暂时不注入真实的服务，因为需要完整的Spring上下文
    // 在实际项目中，应该使用@Autowired注入AuthService

    /**
     * 测试登录请求对象的创建和验证
     */
    @Test
    public void testLoginRequestCreation() {
        // 测试邮箱密码登录请求
        LoginRequest emailRequest = LoginRequest.emailPassword("<EMAIL>", "password123");
        assertNotNull(emailRequest);
        assertEquals("<EMAIL>", emailRequest.getAccount());
        assertEquals("password123", emailRequest.getPassword());
        assertEquals(AuthConstant.LoginType.EMAIL_PASSWORD, emailRequest.getLoginType());
        assertTrue(emailRequest.isPasswordLogin());
        assertFalse(emailRequest.isCaptchaLogin());
        assertFalse(emailRequest.isThirdPartyLogin());

        // 测试手机验证码登录请求
        LoginRequest phoneRequest = LoginRequest.phoneCaptcha("***********", "123456");
        assertNotNull(phoneRequest);
        assertEquals("***********", phoneRequest.getAccount());
        assertEquals("123456", phoneRequest.getCaptcha());
        assertEquals(AuthConstant.LoginType.MOBILE_CAPTCHA, phoneRequest.getLoginType());
        assertFalse(phoneRequest.isPasswordLogin());
        assertTrue(phoneRequest.isCaptchaLogin());
        assertFalse(phoneRequest.isThirdPartyLogin());

        // 测试第三方登录请求
        LoginRequest thirdPartyRequest = LoginRequest.thirdParty(
                AuthConstant.LoginType.WECHAT, "access_token_123", "openid_123");
        assertNotNull(thirdPartyRequest);
        assertEquals(AuthConstant.LoginType.WECHAT, thirdPartyRequest.getLoginType());
        assertEquals("access_token_123", thirdPartyRequest.getAccessToken());
        assertEquals("openid_123", thirdPartyRequest.getOpenId());
        assertFalse(thirdPartyRequest.isPasswordLogin());
        assertFalse(thirdPartyRequest.isCaptchaLogin());
        assertTrue(thirdPartyRequest.isThirdPartyLogin());
    }

    /**
     * 测试登录请求的链式配置
     */
    @Test
    public void testLoginRequestChaining() {
        LoginRequest request = LoginRequest.emailPassword("<EMAIL>", "password123")
                .clientIp("*************")
                .userAgent("Mozilla/5.0")
                .deviceInfo("device123", "ios")
                .appInfo("1.0.0", "appstore")
                .extraParam("source", "mobile");

        assertEquals("*************", request.getClientIp());
        assertEquals("Mozilla/5.0", request.getUserAgent());
        assertEquals("device123", request.getDeviceId());
        assertEquals("ios", request.getDeviceType());
        assertEquals("1.0.0", request.getAppVersion());
        assertEquals("appstore", request.getChannel());
        assertNotNull(request.getExtraParams());
        assertEquals("mobile", request.getExtraParams().get("source"));
    }

    /**
     * 测试登录请求的参数验证
     */
    @Test
    public void testLoginRequestValidation() {
        // 测试有效的邮箱密码登录请求
        LoginRequest validRequest = LoginRequest.emailPassword("<EMAIL>", "password123");
        assertDoesNotThrow(() -> validRequest.validate());

        // 测试无效的请求（空账号）
        LoginRequest invalidRequest1 = new LoginRequest();
        invalidRequest1.setAccount("");
        invalidRequest1.setLoginType(AuthConstant.LoginType.EMAIL_PASSWORD);
        invalidRequest1.setPassword("password123");
        assertThrows(IllegalArgumentException.class, () -> invalidRequest1.validate());

        // 测试无效的请求（空登录类型）
        LoginRequest invalidRequest2 = new LoginRequest();
        invalidRequest2.setAccount("<EMAIL>");
        invalidRequest2.setLoginType("");
        invalidRequest2.setPassword("password123");
        assertThrows(IllegalArgumentException.class, () -> invalidRequest2.validate());

        // 测试无效的请求（密码登录但无密码）
        LoginRequest invalidRequest3 = new LoginRequest();
        invalidRequest3.setAccount("<EMAIL>");
        invalidRequest3.setLoginType(AuthConstant.LoginType.EMAIL_PASSWORD);
        invalidRequest3.setPassword("");
        assertThrows(IllegalArgumentException.class, () -> invalidRequest3.validate());

        // 测试无效的请求（验证码登录但无验证码）
        LoginRequest invalidRequest4 = new LoginRequest();
        invalidRequest4.setAccount("***********");
        invalidRequest4.setLoginType(AuthConstant.LoginType.MOBILE_CAPTCHA);
        invalidRequest4.setCaptcha("");
        assertThrows(IllegalArgumentException.class, () -> invalidRequest4.validate());
    }

    /**
     * 测试账号类型识别
     */
    @Test
    public void testAccountTypeRecognition() {
        // 测试邮箱识别
        LoginRequest emailRequest = new LoginRequest();
        emailRequest.setAccount("<EMAIL>");
        assertEquals("email", emailRequest.getAccountType());

        // 测试手机号识别
        LoginRequest phoneRequest = new LoginRequest();
        phoneRequest.setAccount("***********");
        assertEquals("phone", phoneRequest.getAccountType());

        // 测试用户名识别
        LoginRequest usernameRequest = new LoginRequest();
        usernameRequest.setAccount("testuser");
        assertEquals("username", usernameRequest.getAccountType());

        // 测试未知类型
        LoginRequest unknownRequest = new LoginRequest();
        unknownRequest.setAccount("123");
        assertEquals("username", unknownRequest.getAccountType()); // 短字符串被识别为用户名
    }

    /**
     * 测试认证结果对象
     */
    @Test
    public void testAuthResult() {
        // 测试成功结果
        AuthResult successResult = AuthResult.success();
        assertTrue(successResult.getSuccess());
        assertNotNull(successResult.getTimestamp());

        // 测试失败结果
        AuthResult failureResult = AuthResult.failure(
                AuthConstant.ErrorCode.PASSWORD_ERROR, "密码错误");
        assertFalse(failureResult.getSuccess());
        assertEquals(AuthConstant.ErrorCode.PASSWORD_ERROR, failureResult.getErrorCode());
        assertEquals("密码错误", failureResult.getErrorMessage());

        // 测试链式配置
        AuthResult chainedResult = AuthResult.success()
                .requestId("req123")
                .tokens("access_token", "refresh_token")
                .tokenExpires(3600L, 7200L)
                .loginInfo("email_password", "*************", "web")
                .newUser(false);

        assertEquals("req123", chainedResult.getRequestId());
        assertEquals("access_token", chainedResult.getAccessToken());
        assertEquals("refresh_token", chainedResult.getRefreshToken());
        assertEquals(3600L, chainedResult.getExpiresIn());
        assertEquals(7200L, chainedResult.getRefreshExpiresIn());
        assertEquals("email_password", chainedResult.getLoginType());
        assertEquals("*************", chainedResult.getLoginIp());
        assertEquals("web", chainedResult.getLoginDevice());
        assertFalse(chainedResult.getIsNewUser());
    }

    /**
     * 测试认证结果的便捷方法
     */
    @Test
    public void testAuthResultConvenienceMethods() {
        // 测试登录成功判断
        AuthResult successResult = AuthResult.success()
                .tokens("access_token", "refresh_token")
                .extraInfo("userId", 123L);
        successResult.setUserId(123L);
        assertTrue(successResult.isLoginSuccess());

        // 测试登录失败判断
        AuthResult failureResult = AuthResult.failure(
                AuthConstant.ErrorCode.PASSWORD_ERROR, "密码错误");
        assertFalse(failureResult.isLoginSuccess());

        // 测试错误类型判断
        AuthResult captchaResult = AuthResult.failure(
                AuthConstant.ErrorCode.NEED_CAPTCHA, "需要验证码");
        assertTrue(captchaResult.needCaptcha());

        AuthResult lockedResult = AuthResult.failure(
                AuthConstant.ErrorCode.ACCOUNT_LOCKED, "账号被锁定");
        assertTrue(lockedResult.isAccountLocked());

        AuthResult passwordErrorResult = AuthResult.failure(
                AuthConstant.ErrorCode.PASSWORD_ERROR, "密码错误");
        assertTrue(passwordErrorResult.isPasswordError());

        AuthResult notFoundResult = AuthResult.failure(
                AuthConstant.ErrorCode.ACCOUNT_NOT_FOUND, "账号不存在");
        assertTrue(notFoundResult.isAccountNotFound());
    }

    /**
     * 测试认证结果转换为简化Map
     */
    @Test
    public void testAuthResultToSimpleMap() {
        // 测试成功结果转换
        AuthResult successResult = AuthResult.success()
                .tokens("access_token", "refresh_token")
                .tokenExpires(3600L, 7200L)
                .newUser(true);
        successResult.setUserId(123L);

        Map<String, Object> simpleMap = successResult.toSimpleMap();
        assertTrue((Boolean) simpleMap.get("success"));
        assertEquals("access_token", simpleMap.get("accessToken"));
        assertEquals("refresh_token", simpleMap.get("refreshToken"));
        assertEquals("Bearer", simpleMap.get("tokenType"));
        assertEquals(3600L, simpleMap.get("expiresIn"));
        assertTrue((Boolean) simpleMap.get("isNewUser"));
        assertNotNull(simpleMap.get("timestamp"));

        // 测试失败结果转换
        AuthResult failureResult = AuthResult.failure(
                AuthConstant.ErrorCode.PASSWORD_ERROR, "密码错误");

        Map<String, Object> failureMap = failureResult.toSimpleMap();
        assertFalse((Boolean) failureMap.get("success"));
        assertEquals(AuthConstant.ErrorCode.PASSWORD_ERROR, failureMap.get("errorCode"));
        assertEquals("密码错误", failureMap.get("errorMessage"));
        assertNotNull(failureMap.get("timestamp"));
    }

    /**
     * 测试常量定义
     */
    @Test
    public void testConstants() {
        // 测试登录方式常量
        assertEquals("byemail_password", AuthConstant.LoginType.EMAIL_PASSWORD);
        assertEquals("byemail", AuthConstant.LoginType.EMAIL_CAPTCHA);
        assertEquals("bymobile_password", AuthConstant.LoginType.MOBILE_PASSWORD);
        assertEquals("bysms", AuthConstant.LoginType.MOBILE_CAPTCHA);
        assertEquals("byweixin", AuthConstant.LoginType.WECHAT);

        // 测试错误码常量
        assertEquals("SUCCESS", AuthConstant.ErrorCode.SUCCESS);
        assertEquals("PASSWORD_ERROR", AuthConstant.ErrorCode.PASSWORD_ERROR);
        assertEquals("ACCOUNT_LOCKED", AuthConstant.ErrorCode.ACCOUNT_LOCKED);
        assertEquals("NEED_CAPTCHA", AuthConstant.ErrorCode.NEED_CAPTCHA);

        // 测试验证码类型常量
        assertEquals("login", AuthConstant.CaptchaType.LOGIN);
        assertEquals("register", AuthConstant.CaptchaType.REGISTER);
        assertEquals("reset_password", AuthConstant.CaptchaType.RESET_PASSWORD);

        // 测试默认值常量
        assertEquals(3600L, AuthConstant.Default.TOKEN_EXPIRATION);
        assertEquals(7200L, AuthConstant.Default.REFRESH_TOKEN_EXPIRATION);
        assertEquals(5, AuthConstant.Default.MAX_LOGIN_ATTEMPTS);
        assertEquals(30, AuthConstant.Default.ACCOUNT_LOCK_MINUTES);
    }
}
