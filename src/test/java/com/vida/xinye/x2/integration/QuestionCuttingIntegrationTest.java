package com.vida.xinye.x2.integration;

import com.vida.xinye.x2.adapter.QuestionCuttingAdapter;
import com.vida.xinye.x2.config.QuestionCuttingConfig;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import com.vida.xinye.x2.dto.param.QuestionCuttingParam;
import com.vida.xinye.x2.enums.QuestionCuttingEnum;
import com.vida.xinye.x2.service.QuestionCuttingService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 智能切题集成测试
 * 验证阿里云功能不会影响天壤和有道
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
@SpringBootTest
@ActiveProfiles("test")
class QuestionCuttingIntegrationTest {

    @Autowired
    private QuestionCuttingService questionCuttingService;

    @Autowired
    private List<QuestionCuttingAdapter> adapters;

    @Autowired
    private QuestionCuttingConfig config;

    @Test
    void testAllAdaptersAvailable() {
        // 验证所有适配器都能正常加载
        assertNotNull(adapters);
        assertFalse(adapters.isEmpty());
        
        // 验证包含三个提供商
        boolean hasTianrang = false;
        boolean hasYoudao = false;
        boolean hasAliyun = false;
        
        for (QuestionCuttingAdapter adapter : adapters) {
            switch (adapter.getProvider()) {
                case TIANRANG:
                    hasTianrang = true;
                    break;
                case YOUDAO:
                    hasYoudao = true;
                    break;
                case ALIYUN:
                    hasAliyun = true;
                    break;
            }
        }
        
        assertTrue(hasTianrang, "天壤适配器应该存在");
        assertTrue(hasYoudao, "有道适配器应该存在");
        assertTrue(hasAliyun, "阿里云适配器应该存在");
    }

    @Test
    void testConfigurationLoaded() {
        // 验证配置正确加载
        assertNotNull(config);
        assertNotNull(config.getTianrang());
        assertNotNull(config.getYoudao());
        assertNotNull(config.getAliyun());
        assertNotNull(config.getCommon());
    }

    @Test
    void testTianrangAdapterNotAffected() {
        // 验证天壤适配器功能正常
        QuestionCuttingAdapter tianrangAdapter = adapters.stream()
            .filter(adapter -> adapter.getProvider() == QuestionCuttingEnum.Provider.TIANRANG)
            .findFirst()
            .orElse(null);
        
        assertNotNull(tianrangAdapter);
        assertEquals(QuestionCuttingEnum.Provider.TIANRANG, tianrangAdapter.getProvider());
        assertEquals(10, tianrangAdapter.getPriority());
        assertEquals(5, tianrangAdapter.getQualityScore());
    }

    @Test
    void testYoudaoAdapterNotAffected() {
        // 验证有道适配器功能正常
        QuestionCuttingAdapter youdaoAdapter = adapters.stream()
            .filter(adapter -> adapter.getProvider() == QuestionCuttingEnum.Provider.YOUDAO)
            .findFirst()
            .orElse(null);
        
        assertNotNull(youdaoAdapter);
        assertEquals(QuestionCuttingEnum.Provider.YOUDAO, youdaoAdapter.getProvider());
        assertEquals(20, youdaoAdapter.getPriority());
        assertEquals(3, youdaoAdapter.getQualityScore());
    }

    @Test
    void testAliyunAdapterProperties() {
        // 验证阿里云适配器属性正确
        QuestionCuttingAdapter aliyunAdapter = adapters.stream()
            .filter(adapter -> adapter.getProvider() == QuestionCuttingEnum.Provider.ALIYUN)
            .findFirst()
            .orElse(null);
        
        assertNotNull(aliyunAdapter);
        assertEquals(QuestionCuttingEnum.Provider.ALIYUN, aliyunAdapter.getProvider());
        assertEquals(30, aliyunAdapter.getPriority());
        assertEquals(4, aliyunAdapter.getQualityScore());
        assertEquals(5000, aliyunAdapter.getAverageResponseTime());
    }

    @Test
    void testServiceGetSupportedProviders() {
        // 验证服务能够返回所有支持的提供商
        List<String> supportedProviders = questionCuttingService.getSupportedProviders();
        
        assertNotNull(supportedProviders);
        assertFalse(supportedProviders.isEmpty());
        assertTrue(supportedProviders.contains("tianrang"));
        assertTrue(supportedProviders.contains("youdao"));
        assertTrue(supportedProviders.contains("aliyun"));
    }

    @Test
    void testCutQuestionWithSpecifiedProvider_Tianrang() {
        // 测试指定天壤提供商
        if (!config.getTianrang().isEnabled()) {
            return; // 如果天壤未启用，跳过测试
        }
        
        QuestionCuttingParam param = new QuestionCuttingParam();
        param.setImageBase64("dGVzdF9pbWFnZV9kYXRh"); // test_image_data的Base64
        param.setStrategy("SPECIFIED");
        param.setProvider("tianrang");
        
        // 这里只测试参数验证，不实际调用API
        assertDoesNotThrow(() -> {
            // 验证参数构建正常
            assertNotNull(param.getImageBase64());
            assertEquals("SPECIFIED", param.getStrategy());
            assertEquals("tianrang", param.getProvider());
        });
    }

    @Test
    void testCutQuestionWithSpecifiedProvider_Youdao() {
        // 测试指定有道提供商
        if (!config.getYoudao().isEnabled()) {
            return; // 如果有道未启用，跳过测试
        }
        
        QuestionCuttingParam param = new QuestionCuttingParam();
        param.setImageBase64("dGVzdF9pbWFnZV9kYXRh"); // test_image_data的Base64
        param.setStrategy("SPECIFIED");
        param.setProvider("youdao");
        
        // 这里只测试参数验证，不实际调用API
        assertDoesNotThrow(() -> {
            // 验证参数构建正常
            assertNotNull(param.getImageBase64());
            assertEquals("SPECIFIED", param.getStrategy());
            assertEquals("youdao", param.getProvider());
        });
    }

    @Test
    void testCutQuestionWithSpecifiedProvider_Aliyun() {
        // 测试指定阿里云提供商
        if (!config.getAliyun().isEnabled()) {
            return; // 如果阿里云未启用，跳过测试
        }
        
        QuestionCuttingParam param = new QuestionCuttingParam();
        param.setImageBase64("dGVzdF9pbWFnZV9kYXRh"); // test_image_data的Base64
        param.setStrategy("SPECIFIED");
        param.setProvider("aliyun");
        param.setSubject("math");
        
        // 这里只测试参数验证，不实际调用API
        assertDoesNotThrow(() -> {
            // 验证参数构建正常
            assertNotNull(param.getImageBase64());
            assertEquals("SPECIFIED", param.getStrategy());
            assertEquals("aliyun", param.getProvider());
            assertEquals("math", param.getSubject());
        });
    }

    @Test
    void testBestQualityStrategy() {
        // 测试最佳质量策略选择
        QuestionCuttingParam param = new QuestionCuttingParam();
        param.setImageBase64("dGVzdF9pbWFnZV9kYXRh");
        param.setStrategy("BEST_QUALITY");
        
        // 验证策略设置正确
        assertEquals("BEST_QUALITY", param.getStrategy());
        
        // 在BEST_QUALITY策略下，应该选择质量评分最高的适配器
        // 天壤(5) > 阿里云(4) > 有道(3)
        // 所以应该优先选择天壤
    }

    @Test
    void testFastestStrategy() {
        // 测试最快速度策略选择
        QuestionCuttingParam param = new QuestionCuttingParam();
        param.setImageBase64("dGVzdF9pbWFnZV9kYXRh");
        param.setStrategy("FASTEST");
        
        // 验证策略设置正确
        assertEquals("FASTEST", param.getStrategy());
        
        // 在FASTEST策略下，应该选择响应时间最短的适配器
    }

    @Test
    void testRoundRobinStrategy() {
        // 测试轮询策略
        QuestionCuttingParam param = new QuestionCuttingParam();
        param.setImageBase64("dGVzdF9pbWFnZV9kYXRh");
        param.setStrategy("ROUND_ROBIN");
        
        // 验证策略设置正确
        assertEquals("ROUND_ROBIN", param.getStrategy());
    }

    @Test
    void testAliyunConfigurationProperties() {
        // 验证阿里云配置属性
        QuestionCuttingConfig.AliyunConfig aliyunConfig = config.getAliyun();
        
        assertNotNull(aliyunConfig);
        assertNotNull(aliyunConfig.getEndpoint());
        assertNotNull(aliyunConfig.getRegionId());
        assertNotNull(aliyunConfig.getProduct());
        assertNotNull(aliyunConfig.getVersion());
        assertNotNull(aliyunConfig.getAction());
        assertNotNull(aliyunConfig.getNeedRotate());
        assertNotNull(aliyunConfig.getOutputOricoord());
        
        // 验证默认值
        assertEquals("cn-hangzhou", aliyunConfig.getRegionId());
        assertEquals("ocr-api", aliyunConfig.getProduct());
        assertEquals("2021-07-07", aliyunConfig.getVersion());
        assertEquals("RecognizeEduPaperStructed", aliyunConfig.getAction());
        assertEquals(false, aliyunConfig.getNeedRotate());
        assertEquals(false, aliyunConfig.getOutputOricoord());
    }
}
