package com.vida.xinye.x2.performance;

import com.vida.xinye.x2.adapter.QuestionCuttingAdapter;
import com.vida.xinye.x2.enums.QuestionCuttingEnum;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 智能切题性能测试
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
@SpringBootTest
@ActiveProfiles("test")
class QuestionCuttingPerformanceTest {

    @Autowired
    private List<QuestionCuttingAdapter> adapters;

    @Test
    void testAdapterInitializationPerformance() {
        // 测试适配器初始化性能
        StopWatch stopWatch = new StopWatch("适配器初始化性能测试");
        
        stopWatch.start("获取所有适配器");
        assertNotNull(adapters);
        assertFalse(adapters.isEmpty());
        stopWatch.stop();
        
        stopWatch.start("检查适配器可用性");
        for (QuestionCuttingAdapter adapter : adapters) {
            // 检查可用性不应该抛出异常
            assertDoesNotThrow(adapter::isAvailable);
        }
        stopWatch.stop();
        
        stopWatch.start("获取适配器属性");
        for (QuestionCuttingAdapter adapter : adapters) {
            assertNotNull(adapter.getProvider());
            assertTrue(adapter.getPriority() > 0);
            assertTrue(adapter.getQualityScore() > 0);
            assertTrue(adapter.getAverageResponseTime() > 0);
        }
        stopWatch.stop();
        
        System.out.println(stopWatch.prettyPrint());
        
        // 验证总时间不超过1秒
        assertTrue(stopWatch.getTotalTimeMillis() < 1000, 
                  "适配器初始化时间过长: " + stopWatch.getTotalTimeMillis() + "ms");
    }

    @Test
    void testConcurrentAdapterAccess() {
        // 测试并发访问适配器的性能
        int threadCount = 10;
        int requestsPerThread = 100;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        StopWatch stopWatch = new StopWatch("并发访问测试");
        
        stopWatch.start("并发访问适配器属性");
        
        CompletableFuture<Void>[] futures = IntStream.range(0, threadCount)
            .mapToObj(i -> CompletableFuture.runAsync(() -> {
                for (int j = 0; j < requestsPerThread; j++) {
                    for (QuestionCuttingAdapter adapter : adapters) {
                        // 并发访问适配器属性
                        adapter.getProvider();
                        adapter.getPriority();
                        adapter.getQualityScore();
                        adapter.getAverageResponseTime();
                        adapter.isAvailable();
                    }
                }
            }, executor))
            .toArray(CompletableFuture[]::new);
        
        // 等待所有任务完成
        CompletableFuture.allOf(futures).join();
        
        stopWatch.stop();
        executor.shutdown();
        
        System.out.println(stopWatch.prettyPrint());
        
        // 验证并发访问性能
        long totalRequests = (long) threadCount * requestsPerThread * adapters.size() * 5; // 5个属性
        double requestsPerSecond = totalRequests * 1000.0 / stopWatch.getTotalTimeMillis();
        
        System.out.println("总请求数: " + totalRequests);
        System.out.println("每秒请求数: " + requestsPerSecond);
        
        // 验证每秒至少能处理10000个请求
        assertTrue(requestsPerSecond > 10000, 
                  "并发访问性能不足: " + requestsPerSecond + " requests/second");
    }

    @Test
    void testAdapterSelectionPerformance() {
        // 测试适配器选择性能
        StopWatch stopWatch = new StopWatch("适配器选择性能测试");
        
        stopWatch.start("按优先级排序");
        List<QuestionCuttingAdapter> sortedByPriority = adapters.stream()
            .sorted((a, b) -> Integer.compare(a.getPriority(), b.getPriority()))
            .toList();
        assertFalse(sortedByPriority.isEmpty());
        stopWatch.stop();
        
        stopWatch.start("按质量评分排序");
        List<QuestionCuttingAdapter> sortedByQuality = adapters.stream()
            .sorted((a, b) -> Integer.compare(b.getQualityScore(), a.getQualityScore()))
            .toList();
        assertFalse(sortedByQuality.isEmpty());
        stopWatch.stop();
        
        stopWatch.start("按响应时间排序");
        List<QuestionCuttingAdapter> sortedByResponseTime = adapters.stream()
            .sorted((a, b) -> Long.compare(a.getAverageResponseTime(), b.getAverageResponseTime()))
            .toList();
        assertFalse(sortedByResponseTime.isEmpty());
        stopWatch.stop();
        
        stopWatch.start("筛选可用适配器");
        List<QuestionCuttingAdapter> availableAdapters = adapters.stream()
            .filter(QuestionCuttingAdapter::isAvailable)
            .toList();
        // 在测试环境中，适配器可能不可用，所以不验证数量
        stopWatch.stop();
        
        stopWatch.start("按提供商筛选");
        QuestionCuttingAdapter aliyunAdapter = adapters.stream()
            .filter(adapter -> adapter.getProvider() == QuestionCuttingEnum.Provider.ALIYUN)
            .findFirst()
            .orElse(null);
        assertNotNull(aliyunAdapter);
        stopWatch.stop();
        
        System.out.println(stopWatch.prettyPrint());
        
        // 验证选择操作总时间不超过100毫秒
        assertTrue(stopWatch.getTotalTimeMillis() < 100, 
                  "适配器选择时间过长: " + stopWatch.getTotalTimeMillis() + "ms");
    }

    @Test
    void testMemoryUsage() {
        // 测试内存使用情况
        Runtime runtime = Runtime.getRuntime();
        
        // 强制垃圾回收
        System.gc();
        long memoryBefore = runtime.totalMemory() - runtime.freeMemory();
        
        // 创建大量适配器引用
        for (int i = 0; i < 1000; i++) {
            for (QuestionCuttingAdapter adapter : adapters) {
                // 访问适配器属性
                adapter.getProvider();
                adapter.getPriority();
                adapter.getQualityScore();
                adapter.getAverageResponseTime();
            }
        }
        
        // 再次强制垃圾回收
        System.gc();
        long memoryAfter = runtime.totalMemory() - runtime.freeMemory();
        
        long memoryUsed = memoryAfter - memoryBefore;
        System.out.println("内存使用前: " + memoryBefore / 1024 / 1024 + " MB");
        System.out.println("内存使用后: " + memoryAfter / 1024 / 1024 + " MB");
        System.out.println("内存增长: " + memoryUsed / 1024 / 1024 + " MB");
        
        // 验证内存增长不超过10MB
        assertTrue(memoryUsed < 10 * 1024 * 1024, 
                  "内存使用过多: " + memoryUsed / 1024 / 1024 + " MB");
    }

    @Test
    void testAdapterPropertiesConsistency() {
        // 测试适配器属性一致性
        for (QuestionCuttingAdapter adapter : adapters) {
            // 多次调用应该返回相同的结果
            QuestionCuttingEnum.Provider provider1 = adapter.getProvider();
            QuestionCuttingEnum.Provider provider2 = adapter.getProvider();
            assertEquals(provider1, provider2);
            
            int priority1 = adapter.getPriority();
            int priority2 = adapter.getPriority();
            assertEquals(priority1, priority2);
            
            int quality1 = adapter.getQualityScore();
            int quality2 = adapter.getQualityScore();
            assertEquals(quality1, quality2);
            
            long responseTime1 = adapter.getAverageResponseTime();
            long responseTime2 = adapter.getAverageResponseTime();
            assertEquals(responseTime1, responseTime2);
        }
    }

    @Test
    void testAdapterTypeSpecificProperties() {
        // 测试不同类型适配器的特定属性
        for (QuestionCuttingAdapter adapter : adapters) {
            switch (adapter.getProvider()) {
                case TIANRANG:
                    assertEquals(10, adapter.getPriority(), "天壤适配器优先级应该是10");
                    assertEquals(5, adapter.getQualityScore(), "天壤适配器质量评分应该是5");
                    break;
                case YOUDAO:
                    assertEquals(20, adapter.getPriority(), "有道适配器优先级应该是20");
                    assertEquals(3, adapter.getQualityScore(), "有道适配器质量评分应该是3");
                    break;
                case ALIYUN:
                    assertEquals(30, adapter.getPriority(), "阿里云适配器优先级应该是30");
                    assertEquals(4, adapter.getQualityScore(), "阿里云适配器质量评分应该是4");
                    assertEquals(5000, adapter.getAverageResponseTime(), "阿里云适配器平均响应时间应该是5000ms");
                    break;
            }
        }
    }
}
