<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.vida</groupId>
    <artifactId>xinye-x2</artifactId>
    <version>1.0</version>
    <packaging>jar</packaging>
    <name>xinye-x2</name>
    <description>芯烨家用X2 project</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <skipTests>true</skipTests>
        <docker.host>https://*************:2376</docker.host>
        <docker.maven.plugin.version>0.40.0</docker.maven.plugin.version>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.6.3</spring-boot.version>
        <spring-cloud.version>2021.0.1</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.1.0</spring-cloud-alibaba.version>
        <pagehelper-starter.version>1.4.2</pagehelper-starter.version>
        <pagehelper.version>5.3.0</pagehelper.version>
        <druid.version>1.2.9</druid.version>
        <spring-data-commons.version>2.6.3</spring-data-commons.version>
        <hutool.version>5.8.0</hutool.version>
        <mybatis-generator.version>1.4.1</mybatis-generator.version>
        <mybatis.version>3.5.9</mybatis.version>
        <mysql-connector.version>8.0.29</mysql-connector.version>
        <lombok.version>1.18.22</lombok.version>
        <nimbus-jose-jwt.version>9.23</nimbus-jose-jwt.version>
        <docker.env>xprinter</docker.env>
        <aliyun-oss.version>3.15.0</aliyun-oss.version>
        <aliyun-log-producer.version>0.3.10</aliyun-log-producer.version>
        <aliyun-log.version>0.6.35</aliyun-log.version>
        <aliyun-log-logback-appender.version>0.1.18</aliyun-log-logback-appender.version>
        <aliyun-ocr-api.version>3.1.3</aliyun-ocr-api.version>
        <javax.mail.version>1.6.2</javax.mail.version>
        <jjwt.verion>0.9.1</jjwt.verion>
    </properties>
    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <!-- spring cloud alibaba依赖配置 -->
        <!-- 配置通过nacos读取config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- 远程接口调用 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
        </dependency>

        <!-- 数据库开发相关依赖 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>


        <!-- 阿里云oss -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>${aliyun-oss.version}</version>
        </dependency>

        <!-- 辅助工具 -->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.nimbusds</groupId>
            <artifactId>nimbus-jose-jwt</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- aliYun log producer -->
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>aliyun-log-producer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>aliyun-log</artifactId>
            <classifier>jar-with-dependencies</classifier>
        </dependency>
        <!-- aliyun log logback appender -->
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>aliyun-log-logback-appender</artifactId>
        </dependency>

        <!-- Spring Boot Starter Mail -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- Spring Boot Starter Data Redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <!-- Spring Boot Starter Security -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

        <!-- JWT Library -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>${jjwt.verion}</version>
        </dependency>

        <!-- 学科网 -->
        <dependency>
            <groupId>com.xkw.xop</groupId>
            <artifactId>xkw-xop-client</artifactId>
            <version>1.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.xkw.xop</groupId>
            <artifactId>xkw-xop-qbmsdk</artifactId>
            <version>1.0.5</version>
        </dependency>

        <!-- 阿里云短信服务 -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dysmsapi20170525</artifactId>
            <version>2.0.24</version>
        </dependency>

        <!-- 阿里云OCR API SDK -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>ocr_api20210707</artifactId>
            <version>${aliyun-ocr-api.version}</version>
        </dependency>
    </dependencies>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--Spring Cloud 相关依赖-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--Spring Cloud Alibaba 相关依赖-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--JWT(Json Web Token)登录支持-->
            <dependency>
                <groupId>com.nimbusds</groupId>
                <artifactId>nimbus-jose-jwt</artifactId>
                <version>${nimbus-jose-jwt.version}</version>
            </dependency>

            <!-- MyBatis-->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <!-- MyBatis 生成器 -->
            <dependency>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-core</artifactId>
                <version>${mybatis-generator.version}</version>
            </dependency>
            <!--MyBatis分页插件starter-->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper-starter.version}</version>
            </dependency>
            <!--MyBatis分页插件-->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <!--Mysql数据库驱动-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector.version}</version>
            </dependency>

            <!--集成druid连接池-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- Java工具包-->
            <!--SpringData工具包-->
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-commons</artifactId>
                <version>${spring-data-commons.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <!--aliYun log -->
            <dependency>
                <groupId>com.aliyun.openservices</groupId>
                <artifactId>aliyun-log-producer</artifactId>
                <version>${aliyun-log-producer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.openservices</groupId>
                <artifactId>aliyun-log</artifactId>
                <version>${aliyun-log.version}</version>
                <classifier>jar-with-dependencies</classifier>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>2.5.0</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.openservices</groupId>
                <artifactId>aliyun-log-logback-appender</artifactId>
                <version>${aliyun-log-logback-appender.version}</version>
            </dependency>
            <!--aliYun log end-->

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>3.14.9</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                            <configuration>
                                <mainClass>com.vida.xinye.x2.XinyeX2Application</mainClass>
                                <executable>true</executable>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>io.fabric8</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>${docker.maven.plugin.version}</version>
                    <executions>
                        <!--如果想在项目打包时构建镜像添加-->
                        <execution>
                            <id>build-image</id>
                            <phase>package</phase>
                            <goals>
                                <goal>build</goal>
                                <goal>push</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <dockerHost>${docker.host}</dockerHost>
                        <certPath>./config/docker-cert</certPath>
                        <!-- Docker 推送镜像仓库地址-->
                        <pushRegistry>registry-vpc.cn-shenzhen.aliyuncs.com</pushRegistry>
                        <!--认证配置,用于私有registry认证-->
                        <authConfig>
                            <username>深圳市微嗒科技有限公司</username>
                            <password>vida@2021</password>
                        </authConfig>

                        <images>
                            <image>
                                <!--定义镜像名称-->
                                <name>registry-vpc.cn-shenzhen.aliyuncs.com/${docker.env}/${project.artifactId}:${project.version}</name>
                                <!--定义镜像构建行为-->
                                <build>
                                    <!--定义基础镜像-->
                                    <from>java:8</from>
                                    <args>
                                        <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
                                    </args>
                                    <!--定义哪些文件拷贝到容器中-->
                                    <assembly>
                                        <!--定义拷贝到容器的目录-->
                                        <targetDir>/</targetDir>
                                        <!--只拷贝生成的jar包-->
                                        <descriptorRef>artifact</descriptorRef>
                                    </assembly>
                                    <!--定义容器启动命令-->
                                    <entryPoint>
                                        ["sh", "-c", "java $JAVA_OPTS -jar -Dspring.profiles.active=prod /${project.build.finalName}.jar"]
                                    </entryPoint>
                                    <!--定义维护者-->
                                    <maintainer>wangpf</maintainer>
                                </build>
                            </image>
                        </images>
                    </configuration>
                </plugin>

            </plugins>
        </pluginManagement>

    </build>

</project>
